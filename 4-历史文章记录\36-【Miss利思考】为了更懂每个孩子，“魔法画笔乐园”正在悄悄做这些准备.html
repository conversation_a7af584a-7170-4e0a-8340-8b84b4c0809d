<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Miss利思考 - 公众号封面</title>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@400;500;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://html2canvas.hertzen.com/dist/html2canvas.min.js"></script>
    <style>
        body {
            font-family: 'Noto Sans SC', sans-serif;
            background-color: #f5f5f5;
            padding: 20px;
            text-align: center;
        }
        
        .container-wrapper {
            width: 800px;
            margin: 0 auto;
            background-color: white;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        
        .cover-container {
            display: flex;
            width: 100%;
            height: 239px; /* 固定高度，确保清晰显示 */
        }
        
        .main-cover {
            flex: 2.35;
            position: relative;
            background-color: #1a365d; /* 沉稳的深蓝色调 */
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
        }
        
        .share-cover {
            flex: 1;
            position: relative;
            background-color: #1a365d; /* 沉稳的深蓝色调 */
            display: flex;
            justify-content: center;
            align-items: center;
        }
        
        .title {
            color: white;
            text-align: center;
            line-height: 1.5;
            position: relative;
            z-index: 10;
            width: 90%;
        }
        
        .main-title {
            font-size: 24px;
            font-weight: 700;
            margin-bottom: 10px;
        }
        
        .highlight {
            color: #4fc3f7;
            font-weight: 700;
        }
        
        .brand {
            position: absolute;
            bottom: 10px;
            right: 10px;
            color: white;
            font-weight: 700;
            font-size: 12px;
            opacity: 0.8;
            z-index: 10;
        }
        
        .share-text {
            color: white;
            text-align: center;
            font-size: 16px;
            line-height: 1.5;
            font-weight: 700;
        }
        
        /* 科技感装饰 */
        .tech-decoration {
            position: absolute;
            width: 100%;
            height: 100%;
            top: 0;
            left: 0;
            opacity: 0.15;
            z-index: 1;
        }
        
        .tech-grid {
            position: absolute;
            width: 100%;
            height: 100%;
            background-image: linear-gradient(to right, rgba(255,255,255,0.1) 1px, transparent 1px),
                              linear-gradient(to bottom, rgba(255,255,255,0.1) 1px, transparent 1px);
            background-size: 20px 20px;
            opacity: 0.3;
        }
        
        .tech-circle {
            position: absolute;
            border: 2px solid rgba(255,255,255,0.2);
            border-radius: 50%;
        }
        
        .circle-1 {
            width: 40%;
            height: 40%;
            bottom: -10%;
            left: -10%;
        }
        
        .circle-2 {
            width: 25%;
            height: 25%;
            top: 10%;
            right: 5%;
        }
        
        .education-icons {
            position: absolute;
            display: flex;
            justify-content: center;
            align-items: center;
            color: rgba(255,255,255,0.3);
            font-size: 24px;
        }
        
        .icon-1 {
            top: 30px;
            left: 30px;
        }
        
        .icon-2 {
            bottom: 30px;
            right: 30px;
        }
        
        .download-btn {
            margin-top: 20px;
            padding: 10px 20px;
            background-color: #1a365d;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-weight: 500;
            font-size: 16px;
            display: inline-block;
        }
        
        .download-btn:hover {
            background-color: #2c5282;
        }
        
        .download-btn i {
            margin-right: 8px;
        }
        
        .thinking-element {
            position: absolute;
            background-color: rgba(255,255,255,0.1);
            border-radius: 50%;
            width: 40px;
            height: 40px;
            display: flex;
            justify-content: center;
            align-items: center;
            color: rgba(255,255,255,0.6);
            font-size: 20px;
        }
        
        .thought-1 {
            top: 20%;
            right: 25%;
        }
        
        .thought-2 {
            bottom: 30%;
            left: 15%;
        }
        
        .magical-pen {
            position: absolute;
            bottom: 15%;
            left: 50%;
            transform: translateX(-50%);
            font-size: 24px;
            color: rgba(255,255,255,0.4);
            z-index: 2;
        }
        
        /* 静态图片区域 */
        .static-image-container {
            margin-top: 30px;
            border: 1px solid #ddd;
            padding: 10px;
            background-color: white;
        }
        
        .static-image {
            width: 100%;
            max-width: 800px;
        }
    </style>
</head>
<body>
    <h1 style="margin-bottom: 20px; color: #1a365d; font-size: 28px;">Miss利公众号封面预览</h1>
    
    <div class="container-wrapper" id="capture">
        <div class="cover-container">
            <!-- 主封面 -->
            <div class="main-cover">
                <!-- 科技感装饰 -->
                <div class="tech-decoration">
                    <div class="tech-grid"></div>
                    <div class="tech-circle circle-1"></div>
                    <div class="tech-circle circle-2"></div>
                </div>
                
                <!-- 思考元素 -->
                <div class="thinking-element thought-1">
                    <i class="fas fa-lightbulb"></i>
                </div>
                <div class="thinking-element thought-2">
                    <i class="fas fa-brain"></i>
                </div>
                
                <!-- 魔法画笔元素 -->
                <div class="magical-pen">
                    <i class="fas fa-paint-brush"></i>
                </div>
                
                <!-- 教育图标 -->
                <div class="education-icons icon-1">
                    <i class="fas fa-book-open"></i>
                </div>
                <div class="education-icons icon-2">
                    <i class="fas fa-child"></i>
                </div>
                
                <!-- 标题内容 -->
                <div class="title">
                    <div class="main-title">
                        【<span class="highlight">Miss利思考</span>】<br>
                        魔法画笔的"<span class="highlight">学习洞察镜</span>"<br>
                        看见孩子创作背后的秘密
                    </div>
                </div>
                
                <!-- 品牌标识 -->
                <div class="brand">Miss利</div>
            </div>
            
            <!-- 朋友圈分享封面 -->
            <div class="share-cover">
                <!-- 科技感装饰 -->
                <div class="tech-decoration">
                    <div class="tech-grid"></div>
                    <div class="tech-circle circle-1"></div>
                </div>
                
                <!-- 分享文字 -->
                <div class="share-text">
                    【<span class="highlight">Miss利思考</span>】<br>
                    魔法画笔的<br>"<span class="highlight">学习洞察镜</span>"
                </div>
                
                <!-- 品牌标识 -->
                <div class="brand">Miss利</div>
            </div>
        </div>
    </div>
    
    <!-- 下载按钮 -->
    <button class="download-btn" id="downloadBtn">
        <i class="fas fa-download"></i> 下载封面图片
    </button>
    
    <!-- 静态图片备用选项 -->
    <div class="static-image-container">
        <h2 style="margin-bottom: 15px; color: #1a365d;">备用静态图片</h2>
        <img src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAABLAAAADqCAYAAACSQRrEAAAACXBIWXMAAAsTAAALEwEAmpwYAAABFUlEQVR4nO3BMQEAAADCoPVPbQlvoAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAACAG+sUAAEIfnYDAAAAAElFTkSuQmCC" 
             alt="Miss利思考公众号封面" class="static-image">
    </div>
    
    <script>
        // 等待页面加载完成
        window.onload = function() {
            // HTML2Canvas初始化
            setTimeout(function() {
                html2canvas(document.getElementById('capture'), {
                    allowTaint: true,
                    useCORS: true,
                    scale: 2
                }).then(function(canvas) {
                    // 将生成的canvas替换到静态图片区域
                    var dataUrl = canvas.toDataURL('image/png');
                    document.querySelector('.static-image').src = dataUrl;
                    
                    // 设置下载按钮事件
                    document.getElementById('downloadBtn').addEventListener('click', function() {
                        var link = document.createElement('a');
                        link.download = 'MissLi思考-魔法画笔的学习洞察镜.png';
                        link.href = dataUrl;
                        link.click();
                    });
                }).catch(function(err) {
                    console.error('生成图片时发生错误:', err);
                });
            }, 1000); // 延迟1秒执行，确保资源加载完成
        };
    </script>
</body>
</html> 