<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Miss利公众号封面生成</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@400;700;900&display=swap" rel="stylesheet">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script>
    <style>
        body {
            font-family: 'Noto Sans SC', sans-serif;
            background-color: #f0f2f5;
        }
        .gradient-bg {
            background: linear-gradient(135deg, #1E90FF, #4682B4);
        }
        .cover-container {
            /* 3.35:1 aspect ratio (100 / 3.35 = 29.85) */
            aspect-ratio: 3.35 / 1;
        }
        .main-cover {
            aspect-ratio: 2.35 / 1;
        }
        .share-cover {
            aspect-ratio: 1 / 1;
        }
        .text-outline {
            text-shadow: -1px -1px 0 #1E90FF, 1px -1px 0 #1E90FF, -1px 1px 0 #1E90FF, 1px 1px 0 #1E90FF;
        }
    </style>
</head>
<body class="flex flex-col items-center justify-center min-h-screen p-4">

    <div id="capture-area" class="w-full max-w-4xl mx-auto">
        <div class="cover-container w-full bg-white flex shadow-lg">
            <!-- Left Main Cover (2.35:1) -->
            <div class="main-cover h-full gradient-bg text-white p-6 flex flex-col justify-center items-center relative overflow-hidden">
                <div class="absolute top-0 left-0 w-full h-full opacity-10">
                    <svg width="100%" height="100%" xmlns="http://www.w3.org/2000/svg"><defs><pattern id="grid" width="30" height="30" patternUnits="userSpaceOnUse"><path d="M 30 0 L 0 0 0 30" fill="none" stroke="white" stroke-width="0.5"/></pattern></defs><rect width="100%" height="100%" fill="url(#grid)" /></svg>
                </div>
                <div class="relative z-10 text-center w-full flex flex-col justify-center h-full">
                    <h1 class="text-5xl md:text-6xl font-black leading-tight tracking-wider" style="font-size: clamp(2rem, 8vw, 3.75rem);">
                        <span class="block">AI社团毕业</span>
                        <span class="block text-2xl md:text-3xl font-normal my-2">我送孩子们一份</span>
                        <span class="block text-4xl md:text-5xl font-bold">“国赛挑战书”</span>
                    </h1>
                </div>
                <div class="absolute bottom-4 right-4 text-white text-lg font-bold opacity-80 z-10">Miss利</div>
            </div>
            <!-- Right Share Cover (1:1) -->
            <div class="share-cover h-full gradient-bg text-white p-4 flex flex-col justify-center items-center relative overflow-hidden">
                 <div class="absolute top-0 left-0 w-full h-full opacity-10">
                    <svg width="100%" height="100%" xmlns="http://www.w3.org/2000/svg"><defs><pattern id="grid_sm" width="20" height="20" patternUnits="userSpaceOnUse"><path d="M 20 0 L 0 0 0 20" fill="none" stroke="white" stroke-width="0.5"/></pattern></defs><rect width="100%" height="100%" fill="url(#grid_sm)" /></svg>
                </div>
                <div class="relative z-10 text-center w-full">
                     <h2 class="text-2xl md:text-3xl font-black leading-tight" style="font-size: clamp(1rem, 8vw, 1.875rem);">
                        <span class="block">AI社团毕业</span>
                        <span class="block text-lg md:text-xl font-normal my-1">我送孩子们一份</span>
                        <span class="block text-xl md:text-2xl font-bold">“国赛挑战书”</span>
                    </h2>
                </div>
                <div class="absolute bottom-2 right-2 text-white text-sm font-bold opacity-80 z-10">Miss利</div>
            </div>
        </div>
    </div>

    <button id="download-btn" class="mt-8 px-6 py-3 bg-blue-600 text-white font-bold rounded-lg shadow-md hover:bg-blue-700 transition-colors">
        下载封面图片
    </button>

    <script>
        document.getElementById('download-btn').addEventListener('click', function() {
            const captureArea = document.getElementById('capture-area');
            html2canvas(captureArea, {
                backgroundColor: null, // Use transparent background
                scale: 3 // Increase scale for higher resolution
            }).then(canvas => {
                const link = document.createElement('a');
                link.download = 'Miss利_封面_AI社团毕业国赛挑战书.png';
                link.href = canvas.toDataURL('image/png');
                link.click();
            });
        });
    </script>

</body>
</html>