<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>公众号封面 - 豆包分镜图</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@400;700&display=swap" rel="stylesheet">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js" integrity="sha512-BNaRQnYJYiPSqHHDb58B0yaPfCu+Wgds8Gp/gU33kqBtgNS4tSPHuGibyoeqMV/TJlSKda6FXzoEyYGjTe+vXA==" crossorigin="anonymous" referrerpolicy="no-referrer"></script>
    <style>
        body { font-family: 'Noto Sans SC', sans-serif; }
        /* Enforce overall 3.35:1 aspect ratio */
        .aspect-container {
            width: 100%;
            /* padding-bottom: calc(100% / 3.35); /* Height is 1/3.35 of the width */
             aspect-ratio: 3.35 / 1; /* Modern CSS for aspect ratio */
            position: relative;
            overflow: hidden; /* Hide overflow if content exceeds */
            background: linear-gradient(135deg, #1E90FF, #4682B4); /* Tech blue gradient */
        }
        /* Ensure inner content scales - using flexbox/grid for layout */
        .inner-container {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            display: flex;
        }
         /* Left main cover (2.35 part of 3.35 total) */
        .main-cover {
             width: calc(100% * 2.35 / 3.35);
            /* height: 100%; Already full height via flex */
            position: relative; /* For absolute positioning inside */
            display: flex;
            flex-direction: column;
            justify-content: center; /* Center text vertically */
            align-items: center; /* Center text horizontally */
            padding: 5%; /* Add some padding */
            overflow: hidden;
        }
         /* Right share cover (1 part of 3.35 total) */
        .share-cover {
             width: calc(100% * 1 / 3.35);
             /* height: 100%; Already full height via flex */
            position: relative; /* For absolute positioning inside */
            display: flex;
            flex-direction: column;
            justify-content: center; /* Center text vertically */
            align-items: center; /* Center text horizontally */
            padding: 5%; /* Add some padding */
             border-left: 1px solid rgba(255, 255, 255, 0.3); /* Separator */
             overflow: hidden;
        }

        /* Text styling - Scale with container width */
        .title-text {
            /* clamp(min_font_size, preferred_font_size, max_font_size) */
             font-size: clamp(0.5rem, 2.8vw, 2rem); /* Adjust vw and max size */
            font-weight: 700;
            color: white;
            text-align: center;
            line-height: 1.2;
            text-shadow: 1px 1px 3px rgba(0,0,0,0.4);
        }
         .share-title-text {
            font-size: clamp(0.8rem, 3vw, 1.8rem); /* Smaller for share cover */
            font-weight: 700;
            color: white;
            text-align: center;
            line-height: 1.2;
            text-shadow: 1px 1px 3px rgba(0,0,0,0.4);
        }

        /* Brand mark */
        .brand-mark {
            position: absolute;
            bottom: 10px; /* Adjust */
            right: 15px; /* Adjust */
            color: rgba(255, 255, 255, 0.8);
            font-size: clamp(0.6rem, 1.5vw, 1rem); /* Scale brand mark */
            font-weight: bold;
        }
        /* Decorative elements (example: subtle grid lines) */
        .aspect-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image: linear-gradient(rgba(255,255,255,0.05) 1px, transparent 1px), linear-gradient(90deg, rgba(255,255,255,0.05) 1px, transparent 1px);
            background-size: 20px 20px;
            opacity: 0.5;
            pointer-events: none; /* Don't interfere with clicks */
        }
        /* Ensure text is dominant */
        .main-cover .title-text-container {
             width: 80%; /* Adjust to ensure text dominates */
             height: 70%; /* Adjust to ensure text dominates */
             display: flex;
             justify-content: center;
             align-items: center;
        }
        .share-cover .title-text-container {
             width: 90%;
             height: 80%;
             display: flex;
             justify-content: center;
             align-items: center;
        }
         #downloadBtn {
             margin-top: 20px;
             padding: 10px 20px;
             background-color: #1E90FF;
             color: white;
             border: none;
             border-radius: 5px;
             cursor: pointer;
             font-size: 1rem;
         }
         #downloadBtn:hover {
             background-color: #4682B4;
         }
    </style>
</head>
<body class="bg-gray-100 p-10">

    <div id="captureArea" class="aspect-container mx-auto max-w-4xl shadow-lg">
        <div class="inner-container">
            <div class="main-cover">
                 <div class="title-text-container">
                    <h1 class="title-text">
                        <span class="font-bold text-yellow-300">豆包</span>悄悄上线"<span class="font-bold text-yellow-300">分镜图</span>"神器！<br>Miss利带你第一时间申请内测
                    </h1>
                 </div>
                <div class="brand-mark">Miss利</div>
            </div>
            <div class="share-cover">
                 <div class="title-text-container">
                    <h2 class="share-title-text">
                        <span class="font-bold text-yellow-300">豆包</span>悄悄上线"<span class="font-bold text-yellow-300">分镜图</span>"神器！Miss利带你第一时间申请内测
                    </h2>
                 </div>
                 <div class="brand-mark">Miss利</div>
            </div>
        </div>
    </div>

     <div class="text-center">
        <button id="downloadBtn">下载封面图片</button>
     </div>

    <script>
        document.getElementById('downloadBtn').addEventListener('click', function() {
            const element = document.getElementById('captureArea');
            // Extract a simple filename from the title
            const titleElement = element.querySelector('.title-text');
            const rawTitle = titleElement ? titleElement.innerText.split('！')[0] : 'Miss利封面'; // Get first part of title
            const filename = rawTitle.replace(/[^\w\u4e00-\u9fa5]/g, '_').substring(0, 30); // Sanitize and shorten

            html2canvas(element, {
                useCORS: true, // Important for external resources like fonts
                scale: 2, // Increase scale for higher resolution
                 backgroundColor: null, // Use the element's background
                 logging: false // Suppress html2canvas logs in console
            }).then(canvas => {
                const link = document.createElement('a');
                link.download = `${filename}.png`;
                link.href = canvas.toDataURL('image/png');
                link.click();
            }).catch(err => {
                console.error('html2canvas 截图失败:', err);
                alert('图片下载失败，可能是浏览器兼容性问题或网络问题。请检查控制台获取详细错误信息。');
            });
        });
    </script>

</body>
</html> 