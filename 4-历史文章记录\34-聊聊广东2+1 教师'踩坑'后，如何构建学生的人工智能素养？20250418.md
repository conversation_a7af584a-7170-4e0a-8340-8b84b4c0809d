# 构建学生人工智能素养的探索与实践

> 上一篇，我跟大家掏心窝子聊了聊作为一名信息科技老师，在拥抱人工智能教育过程中亲身"踩"过的一些"坑"（点此回顾：[聊聊广东"2+1" | 我的人工智能教师素养亲身'踩坑'笔记](https://mp.weixin.qq.com/s/NNzC_oe0Dv3YCk9Kd9rEaA)）。

在这场人工智能教育的“无人区”探索中，老师们是拿着地图却没有路标的旅人。

那我们又该如何让孩子们在浪潮之巅站稳脚跟，具备适应未来的人工智能素养呢？这成了我"踩坑"之后，思考最多的问题。

"Miss利，AI真的什么都能做吗？"课堂上听到孩子们带着半信半疑的眼神抛出这个问题，我心里想---有戏！我的人工智能教育终于戳到了关键点。

正当我一筹莫展时，广东省及时出台的"2+1"方案中，那份《广东省中小学学生人工智能素养框架》（下称《框架》）给了我们清晰的指引，它就像一张蓝图，为我们构建学生人工智能素养提供了方向。

今天，我想结合自己在一线教学，特别是"AI社团"和"AI创新探索营"中的实践故事，和大家一起解读这份《框架》的四大核心维度——**人智观念、技术实现、智能思维、伦理责任**。

看看我如何能将这些看似高大上的素养要求，融入日常的课堂互动和引导中，帮助孩子们构建起真正面向未来的人工智能素养。

## 一、人智观念：打好AI时代的思想地基

《框架》首先强调"人智观念"，这其实就是孩子们需要建立的AI时代"世界观"——如何看待AI？它有什么价值和局限？我们和AI是什么关系？

面对挑战时有没有信心？所有素养都从这里起步，它决定了一个简单问题：**孩子究竟是驾驭AI，还是被AI牵着鼻子走？**

记得AI绘画课刚开始时，孩子们看到AI能"画"出天马行空的图片，无不惊叹"太神奇了！"。

但很快，当他们发现AI有时会"画"出六条腿的猫，或者完全"不听指挥"时（我们戏称为"抽卡"现象），又会感到困惑。

我心里暗喜，这不正是让孩子们真切感受AI有啥用、有啥不行的绝佳时刻嘛！我们会一起讨论：

> "为什么AI有时很聪明，有时又很'笨'？"
> "它厉害在哪里？又有哪些事情做不好？"

讨论后，孩子们眼睛一亮：**原来AI也会犯错啊！它虽然很厉害，但画不出我心里那种感觉，** 这种发自内心的领悟，比我直接告诉他们**AI不是万能的**有效多了。

同样，起初有孩子会觉得用AI就是"作弊"，或者担心自己会被AI取代。

但在实践中，当他们发现AI可以帮助自己拓展思路、练习口语（比如Maggie创建的"Miss CoCo"智能体）、更生动地理解知识时，他们的**态度发生了转变**。

他们开始愿意尝试与AI协作，主动探索如何更好地利用这个"AI伙伴"。**从'它会取代我'到'它能帮助我'，从敬畏到合作，这转变看似简单，却是孩子们迈向AI时代的第一道门槛。若过不了这道坎，后面的技能都是空中楼阁。**

## 二、技术实现：掌握AI时代的"新工具"

光有正确的观念还不够，《框架》提出的"技术实现"维度，就是要让孩子们掌握必要的"硬核"技能。

简单说，就是让孩子们知道AI怎么'学习'的（数据、算法这些概念），会用常见的AI工具，还能根据不同情况选对工具、用对方法。

**就像学骑自行车，得先知道哪是车把、哪是踏板吧？这都是上路前的基本功。**

当然，给小学生讲算法原理太难了。我会用更直观的方式，比如在"AI社团"里玩"图像识别"游戏，让孩子们通过给图片打标签，初步理解"数据"和"训练"的概念。工具的事说完了吗？

还没！真正的大头在这儿——**工具使用**。一想到孩子们面对那些复杂的AI平台可能一头雾水，我和先生就决定自己动手，开发了专注于AI绘画的"Miss利@魔法画笔乐园"和用于AI对话的"AIctnas"平台。这些平台界面简洁、操作直观，没有广告干扰，孩子们可以专注于创作和交互，而不是被复杂的功能按钮搞得晕头转向。

看到孩子们能轻松上手，快速将想法变成图像或获得AI的回应，这份投入就值了。

掌握工具后，**应用策略**同样关键。我发现，**孩子们最大的挑战往往是如何清晰地向AI表达自己的需求**。

为此，我设计了AI绘画的"创意三步曲"（画什么+长什么样+怎么画）和AI对话的"万能提示词公式"（你是谁&我是谁+我要做什么+我有什么要求）。

这些简单的"公式"就像是与AI高效沟通的钥匙。当孩子们运用这些方法，发现AI更能"听懂"他们的话，生成更符合预期的结果时，那种成就感溢于言表。

**孩子们用上这些方法后，技术运用效率提升了，意外的收获是，他们的逻辑表达能力也跟着提高了。**

## 三、智能思维：练就AI时代的"智慧脑"

如果说"技术实现"是给了孩子们工具，那"智能思维"就是教他们如何更好地使用这些工具，甚至借助工具让自己的思维变得更强大。

《框架》将其定义为人工智能素养的"思维内核"，包括系统分析、设计思维、协同解决问题和人智共生成长。这听起来很抽象，但在实践中却很具体。

在AI搜索课上，我们重点培养孩子的**系统逻辑分析**能力。

当面对"恐龙为什么会灭绝？"这样复杂的问题时，我引导孩子们不再是简单地问AI要答案，而是学习"拆拆乐法"，将大问题分解成"什么时候灭绝的？""有哪些证据？""主流理论是什么？"等小问题逐一探索。

我们还引入了CARCS模型（可信度、准确性、相关性、时效性、来源），**让他们学会批判性地评估AI提供的信息，而不是全盘接收。**

**协同问题解决**则在项目式学习中体现得淋漓尽致。

比如我们曾尝试过的"智能宠物投喂器"项目，孩子们需要分析需求（宠物饥饿的信号），设计方案（如何识别信号并触发投喂），选择合适的技术（模拟语音识别），并不断测试、优化。

孩子们做这个项目时，我在旁边看着，心想：**'这不就是他们在和AI一起解决实际问题吗？'**

说实话，我真没想到这堂课还有意外收获！那天下课后，我看着孩子们的作品，突然意识到一件事。

在AI绘画课上，孩子们为了得到更满意的作品，会不断调整、细化自己的提示词，反思为什么AI没有理解自己的意图。

看着孩子们改了一遍又一遍提示词，我发现他们的表达能力和审美判断在悄悄提升。我在旁边观察时忍不住笑了——

> 这帮小家伙不知不觉中反客为主，他们哪是在用AI啊，分明是在'调教'AI，让它乖乖按自己的想法出牌。

## 四、伦理责任：把稳AI时代的"方向盘"

技术是把双刃剑。《框架》的最后一个维度"伦理责任"，就是要为孩子们系好AI时代的"安全带"，把稳"方向盘"。

就像教孩子过马路一样，要知道'红灯停、绿灯行'的安全规则，要尊重别人的隐私就像不偷看别人日记一样，**更要明白科技应该像阳光一样温暖人心，而不是伤害他人。**

课堂上，我们会讨论一些"灰色地带"。比如AI绘画时，有孩子尝试输入"恐怖"、"暴力"等"黑暗系"提示词。

我们会借此讨论：**AI生成的内容边界在哪里？我们应该创作什么样的内容？**还有孩子们很关心的：**AI画的画，版权算谁的？用AI写作文算不算抄袭？**这些讨论没有标准答案，但能引发孩子们的**伦理思考**。

**智能安全**方面，我们玩"真假大挑战"游戏，让他们学习辨别网络上的虚假信息，了解AI可能被用于诈骗（如AI换脸、合成语音）的风险，提高防范意识。

同时，强调**隐私保护**，告诉孩子们不要随意在网络或AI应用中透露个人敏感信息。

说到底，**如果只教会孩子们用AI，却不教他们'为什么'和'为谁'用，那我这个老师就太失职了。**所以我特别注重引导他们思考技术向善这个问题。

我们会一起畅想：AI除了能画画、聊天，还能为我们的生活带来哪些好处？能不能用AI帮助解决环境问题？能不能设计帮助残障人士的AI工具？

**技术没有善恶，但使用技术的人有选择。我们今天教会孩子们'AI向善'，就是在为明天的社会埋下善意的种子。**这可能是我们作为教育者最重要却最容易被忽视的责任。

## 五、总结与展望：携手共筑人工智能素养

说了这么多，其实这四个方面不是割裂的，它们就像搭积木一样，一块一块垒起来，才能帮孩子们真正应对AI时代的挑战。

**孩子们的人工智能素养就像盖房子，得先打好'人智观念'这个地基，再学会'技术实现'的墙体搭建，然后装上'智能思维'这个屋顶，最后别忘了安上'伦理责任'这扇安全门。一步步来，缺一不可。**

回顾我的教学实践，我越来越深刻地体会到，**构建学生的人工智能素养，绝非仅仅是教会他们使用几个炫酷的工具。更重要的是点燃他们的好奇心，引导他们独立思考，创造实践的机会，并在潜移默化中塑造正确的价值观。**

> AI再厉害，也只是个学习伙伴。谁来思考？是孩子。谁是创造的主角？是孩子。谁对自己的选择负责？还是孩子自己！

教师"踩坑"不可怕，关键是反思之后，我们能为学生们铺设一条更科学、更符合他们成长规律的人工智能素养构建之路。这条路需要我们教育者不断探索，也需要家长的理解与配合。

**未来的世界属于这些拥抱人工智能、善用人工智能、更能驾驭人工智能的孩子们。**

**AI教育不只是教会孩子用工具，而是帮他们点亮在科技浪潮中的那盏灯。**

**灯不够亮，看不清路；灯光太冷，照不暖心。**

**愿我们用智慧和温度，陪孩子们在这片新大陆上，踏出自己的脚印。**

---

## 聊一聊

在陪伴孩子学习和了解人工智能的过程中，您认为在孩子人工智能素养的构建中，哪个环节最关键，或者说您最关注哪个方面呢？

您有什么好的方法、成功的经验，或者遇到的困惑吗？

欢迎在评论区留言分享，我们一起交流，共同成长！

---