<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>让知识在体验中生根发芽</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background-color: #ffffff;
            color: #333;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            display: flex;
            flex-direction: column;
            align-items: center;
        }
        .learning-container {
            background-color: white;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            padding: 30px;
            margin-bottom: 20px;
            width: 100%;
            max-width: 800px;
        }
        h2 {
            text-align: center;
            color: #2c3e50;
            margin-bottom: 5px;
            font-size: 24px;
        }
        .quote-box {
            background-color: #f1f8e9;
            border-radius: 12px;
            padding: 15px 25px;
            margin: 15px auto 30px;
            max-width: 90%;
            position: relative;
            border-left: 4px solid #8bc34a;
        }
        .quote {
            color: #33691e;
            font-style: italic;
            font-size: 18px;
            text-align: center;
            position: relative;
            padding: 0 15px;
        }
        .quote::before, .quote::after {
            content: '"';
            font-size: 24px;
            color: #8bc34a;
            position: absolute;
            top: 0;
        }
        .quote::before {
            left: 0;
        }
        .quote::after {
            right: 0;
        }
        .attribution {
            text-align: right;
            font-size: 14px;
            color: #689f38;
            margin-top: 8px;
            font-weight: bold;
        }
        .cycle-diagram {
            position: relative;
            width: 500px;
            height: 500px;
            margin: 30px auto;
        }
        .circular-path {
            position: absolute;
            width: 400px;
            height: 400px;
            border: 3px dashed #dcdcdc;
            border-radius: 50%;
            top: 50px;
            left: 50px;
        }
        .stage {
            position: absolute;
            width: 110px;
            height: 110px;
            background-color: white;
            border-radius: 50%;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            text-align: center;
            font-size: 14px;
            font-weight: bold;
            transition: all 0.3s ease;
            z-index: 10;
        }
        .stage:hover {
            transform: scale(1.1);
            box-shadow: 0 8px 20px rgba(0,0,0,0.15);
        }
        .stage-1 {
            top: 0;
            left: 195px;
            background-color: #e3f2fd;
            border: 2px solid #2196f3;
            color: #0d47a1;
        }
        .stage-2 {
            top: 195px;
            right: 0;
            background-color: #fff3e0;
            border: 2px solid #ff9800;
            color: #e65100;
        }
        .stage-3 {
            bottom: 0;
            left: 195px;
            background-color: #fce4ec;
            border: 2px solid #e91e63;
            color: #880e4f;
        }
        .stage-4 {
            top: 195px;
            left: 0;
            background-color: #e8f5e9;
            border: 2px solid #4caf50;
            color: #1b5e20;
        }
        .stage-icon {
            font-size: 28px;
            margin-bottom: 5px;
        }
        .center-circle {
            position: absolute;
            width: 140px;
            height: 140px;
            background: radial-gradient(circle, #ffffff 0%, #f9fbe7 100%);
            border-radius: 50%;
            top: 180px;
            left: 180px;
            box-shadow: 0 0 20px rgba(0,0,0,0.05);
            display: flex;
            align-items: center;
            justify-content: center;
            flex-direction: column;
            z-index: 5;
        }
        .center-icon {
            font-size: 60px;
            color: #8bc34a;
            margin-bottom: 5px;
        }
        .center-text {
            font-size: 14px;
            color: #33691e;
            font-weight: bold;
            text-align: center;
        }
        .arrow {
            position: absolute;
            width: 90px;
            height: 20px;
            z-index: 1;
        }
        .arrow-path {
            width: 100%;
            height: 100%;
            position: relative;
        }
        .arrow-1 {
            top: 80px;
            right: 115px;
            transform: rotate(45deg);
        }
        .arrow-2 {
            bottom: 80px;
            right: 115px;
            transform: rotate(135deg);
        }
        .arrow-3 {
            bottom: 80px;
            left: 115px;
            transform: rotate(225deg);
        }
        .arrow-4 {
            top: 80px;
            left: 115px;
            transform: rotate(315deg);
        }
        .arrow-line {
            position: absolute;
            top: 10px;
            left: 0;
            height: 3px;
            width: 70px;
            background-color: #bdbdbd;
        }
        .arrow-head {
            position: absolute;
            top: 3px;
            right: 0;
            width: 0;
            height: 0;
            border-left: 15px solid #bdbdbd;
            border-top: 8px solid transparent;
            border-bottom: 8px solid transparent;
        }
        .example-box {
            background-color: #f5f5f5;
            border-radius: 12px;
            padding: 20px;
            margin-top: 10px;
            position: relative;
        }
        .example-title {
            display: flex;
            align-items: center;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 12px;
            font-size: 16px;
        }
        .example-icon {
            margin-right: 10px;
            color: #e91e63;
            font-size: 18px;
        }
        .example-content {
            background-color: white;
            border-radius: 8px;
            padding: 15px;
            border-left: 3px solid #e91e63;
            font-size: 14px;
            line-height: 1.6;
        }
        .highlight {
            background-color: #fce4ec;
            padding: 2px 4px;
            font-weight: bold;
            color: #880e4f;
        }
        .teacher-note {
            margin-top: 20px;
            padding: 15px;
            background-color: #e8f5e9;
            border-radius: 8px;
            display: flex;
            align-items: flex-start;
        }
        .teacher-icon {
            margin-right: 12px;
            color: #4caf50;
            font-size: 24px;
            flex-shrink: 0;
        }
        .teacher-text {
            font-size: 14px;
            color: #1b5e20;
            line-height: 1.5;
        }
        .download-btn {
            background-color: #3498db;
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            transition: all 0.3s;
            display: flex;
            align-items: center;
            margin: 30px auto 0;
            box-shadow: 0 3px 10px rgba(52,152,219,0.3);
        }
        .download-btn:hover {
            background-color: #2980b9;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(52,152,219,0.4);
        }
        .download-btn svg {
            margin-right: 10px;
        }
        @media (max-width: 768px) {
            .cycle-diagram {
                width: 320px;
                height: 320px;
            }
            .circular-path {
                width: 260px;
                height: 260px;
                top: 30px;
                left: 30px;
            }
            .stage {
                width: 80px;
                height: 80px;
                font-size: 12px;
            }
            .stage-1 {
                left: 120px;
                top: 0;
            }
            .stage-2 {
                top: 120px;
                right: 0;
            }
            .stage-3 {
                bottom: 0;
                left: 120px;
            }
            .stage-4 {
                top: 120px;
                left: 0;
            }
            .center-circle {
                width: 100px;
                height: 100px;
                top: 110px;
                left: 110px;
            }
            .center-icon {
                font-size: 40px;
            }
            .center-text {
                font-size: 10px;
            }
            .arrow {
                width: 70px;
            }
            .arrow-1 {
                top: 60px;
                right: 75px;
            }
            .arrow-2 {
                bottom: 60px;
                right: 75px;
            }
            .arrow-3 {
                bottom: 60px;
                left: 75px;
            }
            .arrow-4 {
                top: 60px;
                left: 75px;
            }
            .arrow-line {
                width: 50px;
            }
            .stage-icon {
                font-size: 20px;
            }
        }
    </style>
    <!-- 使用Font Awesome图标 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
</head>
<body>
    <div class="learning-container" id="learningContainer">
        <h2>让知识在体验中生根发芽</h2>
        
        <div class="quote-box">
            <div class="quote">我们不是说知识不重要，而是要让知识在活动中学活。</div>
            <div class="attribution">— 李教授</div>
        </div>
        
        <div class="cycle-diagram">
            <div class="circular-path"></div>
            
            <!-- 四个学习阶段 -->
            <div class="stage stage-1">
                <div class="stage-icon"><i class="fas fa-book"></i></div>
                <div>理论知识</div>
            </div>
            
            <div class="stage stage-2">
                <div class="stage-icon"><i class="fas fa-hands"></i></div>
                <div>动手实践</div>
            </div>
            
            <div class="stage stage-3">
                <div class="stage-icon"><i class="fas fa-lightbulb"></i></div>
                <div>发现真知</div>
            </div>
            
            <div class="stage stage-4">
                <div class="stage-icon"><i class="fas fa-project-diagram"></i></div>
                <div>系统化总结</div>
            </div>
            
            <!-- 中心图标 -->
            <div class="center-circle">
                <div class="center-icon"><i class="fas fa-seedling"></i></div>
                <div class="center-text">知识生根发芽</div>
            </div>
            
            <!-- 连接箭头 -->
            <div class="arrow arrow-1">
                <div class="arrow-path">
                    <div class="arrow-line"></div>
                    <div class="arrow-head"></div>
                </div>
            </div>
            
            <div class="arrow arrow-2">
                <div class="arrow-path">
                    <div class="arrow-line"></div>
                    <div class="arrow-head"></div>
                </div>
            </div>
            
            <div class="arrow arrow-3">
                <div class="arrow-path">
                    <div class="arrow-line"></div>
                    <div class="arrow-head"></div>
                </div>
            </div>
            
            <div class="arrow arrow-4">
                <div class="arrow-path">
                    <div class="arrow-line"></div>
                    <div class="arrow-head"></div>
                </div>
            </div>
        </div>
        
        <div class="example-box">
            <div class="example-title">
                <i class="example-icon fas fa-star"></i>
                学习案例：数据量与模型效果
            </div>
            
            <div class="example-content">
                当孩子们自己动手训练模型时发现，数据量不够，模型就不好使。这时，<span class="highlight">数据重要性</span>不再是课本上冷冰冰的理论，而是他们亲身验证过的真知灼见。这种从做中学、再回到做的过程，才是培养计算思维的正确打开方式。
            </div>
            
            <div class="teacher-note">
                <i class="teacher-icon fas fa-chalkboard-teacher"></i>
                <div class="teacher-text">
                    教师需要在关键环节帮助孩子们总结和提炼，将分散的实践经验整合成系统的知识架构。这样他们才能真正掌握知识，并在未来的学习中灵活应用。
                </div>
            </div>
        </div>
    </div>

    <button class="download-btn" id="downloadBtn">
        <svg width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
            <polyline points="7 10 12 15 17 10"></polyline>
            <line x1="12" y1="15" x2="12" y2="3"></line>
        </svg>
        下载学习循环图
    </button>

    <script src="https://html2canvas.hertzen.com/dist/html2canvas.min.js"></script>
    <script>
        document.getElementById('downloadBtn').addEventListener('click', function() {
            const element = document.getElementById('learningContainer');
            const originalWidth = element.offsetWidth;
            const originalHeight = element.offsetHeight;
            const scale = 2;
            
            html2canvas(element, {
                backgroundColor: "#ffffff",
                scale: scale,
                width: originalWidth,
                height: originalHeight,
                windowWidth: originalWidth * scale,
                windowHeight: originalHeight * scale
            }).then(canvas => {
                const link = document.createElement('a');
                link.download = '让知识在体验中生根发芽.png';
                link.href = canvas.toDataURL('image/png');
                link.click();
            });
        });
    </script>
</body>
</html>
