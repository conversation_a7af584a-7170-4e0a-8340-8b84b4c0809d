<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>公众号封面</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@400;700;900&family=ZCOOL+QingKe+HuangYou&family=ZCOOL+XiaoWei&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <script src="https://html2canvas.hertzen.com/dist/html2canvas.min.js"></script>
    <style>
        :root {
            --brand-primary: #079452;
            --brand-accent: #FFD966;
            --dark-shade: #054d2b;
            --light-shade: #e0f5ea;
        }

        body {
            font-family: 'ZCOOL QingKe HuangYou', 'Noto Sans SC', sans-serif;
            background-color: #f5f5f5;
            padding: 20px;
        }

        .container-wrapper {
            max-width: 1200px;
            margin: 0 auto;
        }

        .cover-container {
            width: 100%;
            position: relative;
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .aspect-container {
            width: 100%;
            position: relative;
            padding-bottom: 29.85%; /* 10:3.35 aspect ratio */
        }

        .covers-wrapper {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            display: flex;
        }

        .main-cover {
            flex: 2.35;
            background-color: var(--brand-primary);
            position: relative;
            overflow: hidden;
        }

        .circle-cover {
            flex: 1;
            background-color: var(--brand-primary);
            position: relative;
            overflow: hidden;
            margin-left: 2px;
        }

        .circuit-lines {
            position: absolute;
            width: 100%;
            height: 100%;
            opacity: 0.1;
            background-image: 
                linear-gradient(to right, var(--light-shade) 1px, transparent 1px),
                linear-gradient(to bottom, var(--light-shade) 1px, transparent 1px),
                radial-gradient(circle, var(--light-shade) 2px, transparent 2px);
            background-size: 40px 40px, 40px 40px, 200px 200px;
            background-position: center;
        }

        .data-flow {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            overflow: hidden;
        }

        .data-line {
            position: absolute;
            height: 2px;
            background-color: var(--brand-accent);
            opacity: 0.3;
            animation: flowAnimation 8s infinite linear;
        }

        @keyframes flowAnimation {
            0% { transform: translateX(-100%); }
            100% { transform: translateX(100%); }
        }

        .title-container {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 80%;
            text-align: center;
            z-index: 10;
        }

        .main-title {
            font-size: 2.2vw;
            font-weight: 900;
            color: white;
            line-height: 1.4;
            margin-bottom: 0.8vw;
            letter-spacing: 0.03em;
            text-shadow: 2px 2px 0px rgba(0, 0, 0, 0.15);
        }

        .highlight {
            color: var(--brand-accent);
            display: inline-block;
            position: relative;
            font-weight: 900;
        }

        .subtitle {
            font-size: 1.2vw;
            color: rgba(255, 255, 255, 0.8);
            margin-top: 1vw;
        }

        .icon-tag {
            display: inline-flex;
            align-items: center;
            background-color: rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            padding: 0.3vw 0.8vw;
            margin: 0.4vw;
            font-size: 0.9vw;
        }

        .icon-tag i {
            margin-right: 0.3vw;
        }

        .corner-decoration {
            position: absolute;
            width: 15%;
            height: 15%;
            opacity: 0.3;
        }

        .top-left {
            top: 0;
            left: 0;
            border-top: 3px solid var(--brand-accent);
            border-left: 3px solid var(--brand-accent);
            border-top-left-radius: 8px;
        }

        .bottom-right {
            bottom: 0;
            right: 0;
            border-bottom: 3px solid var(--brand-accent);
            border-right: 3px solid var(--brand-accent);
            border-bottom-right-radius: 8px;
        }

        .download-btn {
            display: block;
            margin: 20px auto;
            padding: 10px 24px;
            background-color: var(--brand-primary);
            color: white;
            border: none;
            border-radius: 4px;
            font-size: 16px;
            cursor: pointer;
            transition: background-color 0.3s;
        }

        .download-btn:hover {
            background-color: var(--dark-shade);
        }

        /* 响应式调整 */
        @media (max-width: 768px) {
            .main-title {
                font-size: 3.8vw;
            }
            .subtitle {
                font-size: 1.8vw;
            }
            .icon-tag {
                font-size: 1.4vw;
            }
        }

        @media (max-width: 480px) {
            .main-title {
                font-size: 4.5vw;
            }
            .subtitle {
                font-size: 2.2vw;
            }
            .icon-tag {
                font-size: 1.8vw;
            }
        }
    </style>
</head>
<body>
    <div class="container-wrapper">
        <div id="cover-img" class="cover-container">
            <div class="aspect-container">
                <div class="covers-wrapper">
                    <!-- 主封面 -->
                    <div class="main-cover">
                        <div class="circuit-lines"></div>
                        <div class="data-flow">
                            <div class="data-line" style="top: 20%; width: 60%; animation-delay: 0s;"></div>
                            <div class="data-line" style="top: 35%; width: 40%; animation-delay: 1s;"></div>
                            <div class="data-line" style="top: 50%; width: 80%; animation-delay: 2s;"></div>
                            <div class="data-line" style="top: 65%; width: 50%; animation-delay: 3s;"></div>
                            <div class="data-line" style="top: 80%; width: 70%; animation-delay: 4s;"></div>
                        </div>
                        <div class="corner-decoration top-left"></div>
                        <div class="corner-decoration bottom-right"></div>
                        <div class="title-container">
                            <div class="main-title">
                                赚大钱很难。<br>但，<span class="highlight">赚小钱</span>像呼吸一样简单。
                            </div>
                            <div class="subtitle">
                                <span class="icon-tag"><i class="fas fa-coins"></i> 少亏钱</span>
                                <span class="icon-tag"><i class="fas fa-chart-line"></i> 多赚钱</span>
                                <span class="icon-tag"><i class="fas fa-piggy-bank"></i> 少花冤枉钱</span>
                            </div>
                        </div>
                    </div>
                    <!-- 朋友圈封面 -->
                    <div class="circle-cover">
                        <div class="circuit-lines"></div>
                        <div class="data-flow">
                            <div class="data-line" style="top: 20%; width: 60%; animation-delay: 0.5s;"></div>
                            <div class="data-line" style="top: 50%; width: 70%; animation-delay: 1.5s;"></div>
                            <div class="data-line" style="top: 80%; width: 50%; animation-delay: 2.5s;"></div>
                        </div>
                        <div class="corner-decoration top-left"></div>
                        <div class="corner-decoration bottom-right"></div>
                        <div class="title-container">
                            <div class="main-title" style="font-size: 1.8vw;">
                                赚小钱<br>像呼吸一样简单
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <button id="download-btn" class="download-btn">下载封面图片</button>
    </div>

    <script>
        document.getElementById('download-btn').addEventListener('click', function() {
            html2canvas(document.getElementById('cover-img')).then(function(canvas) {
                var link = document.createElement('a');
                link.download = '公众号封面.png';
                link.href = canvas.toDataURL();
                link.click();
            });
        });

        // 动态生成数据流线条
        window.addEventListener('load', function() {
            const dataFlows = document.querySelectorAll('.data-flow');
            dataFlows.forEach(flow => {
                for (let i = 0; i < 5; i++) {
                    const line = document.createElement('div');
                    line.classList.add('data-line');
                    line.style.top = (Math.random() * 100) + '%';
                    line.style.width = (30 + Math.random() * 50) + '%';
                    line.style.animationDelay = (Math.random() * 5) + 's';
                    flow.appendChild(line);
                }
            });
        });
    </script>
</body>
</html> 