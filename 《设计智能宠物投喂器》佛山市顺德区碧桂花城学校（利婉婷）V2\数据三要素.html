<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据三要素</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background-color: #ffffff;
            color: #333;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            display: flex;
            flex-direction: column;
            align-items: center;
        }
        .data-container {
            background-color: white;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            padding: 30px;
            margin-bottom: 20px;
            width: 100%;
            max-width: 800px;
        }
        h2 {
            text-align: center;
            color: #2c3e50;
            margin-bottom: 25px;
            font-size: 24px;
        }
        .data-elements {
            background-color: #eafaf1;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.05);
        }
        .data-title {
            display: flex;
            align-items: center;
            color: #27ae60;
            font-weight: bold;
            font-size: 18px;
            margin-bottom: 15px;
        }
        .data-icon {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background-color: rgba(39, 174, 96, 0.2);
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 12px;
            font-size: 18px;
            color: #27ae60;
        }
        .data-items {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
        }
        .data-item {
            flex: 1;
            min-width: 150px;
            padding: 12px;
            background-color: white;
            border-radius: 8px;
            border-left: 3px solid #27ae60;
            box-shadow: 0 3px 10px rgba(0,0,0,0.05);
        }
        .data-item-title {
            font-weight: bold;
            margin-bottom: 5px;
            display: flex;
            align-items: center;
        }
        .data-item-icon {
            color: #27ae60;
            margin-right: 8px;
        }
        .data-item-detail {
            font-size: 13px;
            color: #7f8c8d;
            font-style: italic;
        }
        .download-btn {
            background-color: #27ae60;
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            transition: all 0.3s;
            display: flex;
            align-items: center;
            margin: 30px auto 0;
            box-shadow: 0 3px 10px rgba(39, 174, 96, 0.3);
        }
        .download-btn:hover {
            background-color: #219653;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(39, 174, 96, 0.4);
        }
        .download-btn svg {
            margin-right: 10px;
        }
        @media (max-width: 768px) {
            .data-items {
                flex-direction: column;
            }
            .data-item {
                min-width: auto;
            }
        }
    </style>
    <!-- 使用Font Awesome图标 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
</head>
<body>
    <div class="data-container" id="dataContainer">
        <h2></h2>
        
        <div class="data-elements">
            <div class="data-title">
                <div class="data-icon"><i class="fas fa-database"></i></div>
                数据三要素
            </div>
            
            <div class="data-items">
                <div class="data-item">
                    <div class="data-item-title">
                        <i class="data-item-icon fas fa-random"></i>
                        多样性
                    </div>
                    <div class="data-item-detail">不能只喂一种"数据狗粮"</div>
                </div>
                
                <div class="data-item">
                    <div class="data-item-title">
                        <i class="data-item-icon fas fa-layer-group"></i>
                        充足性
                    </div>
                    <div class="data-item-detail">20组样本是最低"饭量"</div>
                </div>
                
                <div class="data-item">
                    <div class="data-item-title">
                        <i class="data-item-icon fas fa-check-circle"></i>
                        高质量
                    </div>
                    <div class="data-item-detail">拒绝"含噪音零食"</div>
                </div>
            </div>
        </div>
    </div>

    <button class="download-btn" id="downloadBtn">
        <svg width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
            <polyline points="7 10 12 15 17 10"></polyline>
            <line x1="12" y1="15" x2="12" y2="3"></line>
        </svg>
        下载数据三要素图
    </button>

    <script src="https://html2canvas.hertzen.com/dist/html2canvas.min.js"></script>
    <script>
        document.getElementById('downloadBtn').addEventListener('click', function() {
            const element = document.getElementById('dataContainer');
            const originalWidth = element.offsetWidth;
            const originalHeight = element.offsetHeight;
            const scale = 2;
            
            html2canvas(element, {
                backgroundColor: "#ffffff",
                scale: scale,
                width: originalWidth,
                height: originalHeight,
                windowWidth: originalWidth * scale,
                windowHeight: originalHeight * scale
            }).then(canvas => {
                const link = document.createElement('a');
                link.download = '数据三要素.png';
                link.href = canvas.toDataURL('image/png');
                link.click();
            });
        });
    </script>
</body>
</html>