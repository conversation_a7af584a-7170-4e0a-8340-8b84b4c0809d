/**
 * Miss 利 - 专业科技教育主题 Claude3.7优化版 (20250611版)
 * 主题色: #1E90FF (深天蓝色)
 * 特点: 针对公众号阅读体验全面优化，增强移动端表现
 */

/* 顶层容器样式 - 针对移动端优化 */
container {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', 'Microsoft YaHei', sans-serif;
  line-height: 1.8; /* 增加行高提升可读性 */
  color: #333333;
  padding: 10px 2px; /* 恢复到v0.1的内边距设置 */
  font-size: 16px; /* 基础字体大小增加 */
  letter-spacing: 0.3px; /* 字间距调整 */
  max-width: 100%; /* 确保容器占据可用宽度 */
  box-sizing: border-box; /* 确保padding计入宽度 */
  text-align: justify; /* 确保整体两端对齐 */
}

/* 移除之前添加的过度限制 */
body, html, .main-content, .article-content, .rich-text {
  max-width: 100%;
  box-sizing: border-box;
}

/* 文章首屏优化 */
.article-header {
  margin-bottom: 2em;
  text-align: center;
}

.article-title {
  font-size: 1.8em;
  font-weight: 700;
  margin-bottom: 0.8em;
  color: #333333;
}

.article-meta {
  color: #666666;
  font-size: 0.9em;
  margin-bottom: 1.5em;
}

.article-cover {
  width: 100%;
  border-radius: 8px;
  margin-bottom: 1.5em;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* 一级标题样式 - 移动端优化 */
h1 {
  color: #333333;
  font-size: 1.7em; /* 增大字号 */
  font-weight: 700; /* 增加粗细 */
  margin-top: 2em; /* 增加上边距 */
  margin-bottom: 1.2em;
  background-color: transparent;
  padding: 0.5em 0;
  border-radius: 0;
  text-align: left;
  border-left: none;
  border-bottom: 3px solid #1E90FF;
  padding-bottom: 0.4em;
  /* 添加微弱阴影提升层次感 */
  text-shadow: 0 1px 1px rgba(0,0,0,0.05);
}

/* 二级标题样式 - 移动端优化 */
h2 {
  color: #333333;
  font-size: 1.5em; /* 增大字号 */
  font-weight: 700;
  margin-top: 1.8em; /* 增加上边距 */
  margin-bottom: 1em;
  background-color: transparent;
  padding: 0.4em 0 0.4em 0.5em; /* 减小左侧内边距 */
  border-radius: 0;
  display: block;
  border: none;
  border-left: 4px solid #1E90FF; /* 加粗左侧边框 */
  text-align: left;
}

/* 三级标题样式 - 移动端优化 */
h3 {
  color: #1E90FF;
  font-size: 1.3em; /* 增大字号 */
  font-weight: 600;
  margin-top: 1.6em;
  margin-bottom: 0.8em;
  border-left: 3px solid #1E90FF;
  padding-left: 0.4em; /* 减小左侧内边距 */
  border-bottom: 1px dotted #1E90FF;
  padding-bottom: 0.3em;
}

/* 新增四级标题样式 */
h4 {
  color: #444444;
  font-size: 1.1em;
  font-weight: 600;
  margin-top: 1.4em;
  margin-bottom: 0.6em;
  border-left: 2px solid rgba(30, 144, 255, 0.6);
  padding-left: 0.4em; /* 减小左侧内边距 */
}

/* 段落样式 - 优化移动端阅读体验 */
p {
  margin-bottom: 1.4em; /* 增加段落间距 */
  letter-spacing: 0.5px;
  line-height: 1.9; /* 增加行高 */
  text-align: justify; /* 两端对齐 */
  word-break: break-word; /* 移动端断词优化 */
  text-indent: 0; /* 确保没有额外的段落缩进 */
}

/* 图片样式 - 移动端友好 */
image, img {
  border-radius: 8px; /* 增加圆角 */
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.12);
  margin: 1.5em auto; /* 居中显示 */
  display: block;
  max-width: 100%;
  height: auto !important; /* 确保高度自动调整 */
}

/* 点击放大提示 */
.img-caption {
  text-align: center;
  color: #666;
  font-size: 0.9em;
  margin-top: -0.8em;
  margin-bottom: 1.5em;
}

/* 引用样式 - 增强视觉效果 */
blockquote {
  border-left: 4px solid #1E90FF;
  background-color: rgba(30, 144, 255, 0.06); /* 稍微加深背景色 */
  color: #555555;
  padding: 15px 12px 15px 15px; /* 减小左右内边距 */
  margin: 1.8em 0; /* 增加外边距 */
  border-radius: 0 6px 6px 0; /* 增加右侧圆角 */
  font-size: 0.95em; /* 略微调整字体大小 */
}

blockquote_p {
  margin-bottom: 0.6em;
}

/* 金句专属样式 - 强化设计 */
.golden-quote {
  border: none;
  background-color: transparent;
  text-align: center;
  padding: 2.5em 1em; /* 增加内边距 */
  margin: 2.5em 0; /* 增加外边距 */
  position: relative;
}

.golden-quote p {
  font-size: 1.3em; /* 增大字号 */
  font-weight: 600;
  color: #1E90FF;
  letter-spacing: 1.2px;
  line-height: 1.6;
  position: relative;
  z-index: 2;
}

.golden-quote::before {
  content: '"';
  font-size: 4em; /* 增大引号大小 */
  color: rgba(30, 144, 255, 0.15);
  position: absolute;
  left: 0;
  top: 0.1em;
  z-index: 1;
  font-family: serif;
}

.golden-quote::after {
  content: '"';
  font-size: 4em;
  color: rgba(30, 144, 255, 0.15);
  position: absolute;
  right: 0;
  bottom: -0.2em;
  z-index: 1;
  font-family: serif;
}

/* 分割线样式 - 更精致 */
hr {
  border: 0;
  height: 1px;
  background-image: linear-gradient(to right, rgba(30, 144, 255, 0), rgba(30, 144, 255, 0.8), rgba(30, 144, 255, 0));
  margin: 2.5em 0; /* 增加外边距 */
}

/* 装饰性分割线 */
.hr-star {
  text-align: center;
  margin: 2.5em 0;
  height: 20px;
  position: relative;
}

.hr-star::before {
  content: "✦";
  color: #1E90FF;
  font-size: 1.2em;
  background: #fff;
  padding: 0 20px;
  position: relative;
  z-index: 2;
}

.hr-star::after {
  content: "";
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  height: 1px;
  background-image: linear-gradient(to right, rgba(30, 144, 255, 0), rgba(30, 144, 255, 0.8), rgba(30, 144, 255, 0));
  z-index: 1;
}

/* 行内代码样式 - 提升可读性 */
codespan, p > code, li > code {
  background-color: rgba(30, 144, 255, 0.1);
  color: #d14;
  padding: .25em .5em; /* 增加内边距 */
  margin: 0 3px; /* 增加外边距 */
  border-radius: 4px;
  font-size: 0.95em;
  font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, Courier, monospace;
  word-break: break-all; /* 移动端断词 */
}

/* 粗体样式 */
strong {
  font-weight: 600;
  color: #1E90FF;
}

/* 强调文本 - 新增多彩效果 */
.highlight-yellow {
  background: linear-gradient(transparent 40%, rgba(255, 250, 150, 0.8) 40%);
  padding: 0 2px;
}

.highlight-blue {
  background: linear-gradient(transparent 40%, rgba(30, 144, 255, 0.2) 40%);
  padding: 0 2px;
}

/* 链接样式 - 更醒目 */
link, wx_link, a {
  color: #1E90FF;
  text-decoration: none;
  border-bottom: 1px dotted #1E90FF;
  padding-bottom: 1px;
  font-weight: 500; /* 增加字重 */
}

link:hover, wx_link:hover, a:hover {
  color: #007acc;
  border-bottom: 1px solid #007acc;
}

/* 列表样式 - 优化移动端体验 */
ul, ol {
  padding-left: 1em; /* 减少左内边距，适应窄屏 */
  margin-bottom: 1.5em; /* 增加底部外边距 */
  margin-top: 0.8em; /* 添加顶部外边距 */
}

listitem, li {
  margin-bottom: 0.7em; /* 增加列表项间距 */
  line-height: 1.7; /* 优化行高 */
}

/* 列表样式美化 */
ul li::marker {
  color: #1E90FF; /* 项目符号使用主题色 */
}

ol li::marker {
  color: #1E90FF; /* 数字使用主题色 */
  font-weight: 600;
}

/* 代码块样式 - 移动端优化 */
code_pre {
  background-color: #f8f9fa;
  border: 1px solid #dee2e6;
  padding: 1.2em; /* 增加内边距 */
  margin: 1.8em 0; /* 增加外边距 */
  border-radius: 8px; /* 增加圆角 */
  overflow-x: auto;
  font-size: 0.9em; /* 略微减小字号以适应移动屏幕 */
  position: relative;
}

code_pre::before {
  content: "代码";
  position: absolute;
  top: -10px;
  left: 10px;
  background: #1E90FF;
  color: white;
  padding: 0 8px;
  font-size: 12px;
  border-radius: 4px;
  font-weight: bold;
}

code_pre code {
  font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, Courier, monospace;
  color: #333333;
  background-color: transparent;
  padding: 0;
  margin: 0;
  line-height: 1.6; /* 优化代码行高 */
}

/* GFM 提示框样式 - 全新设计 */
blockquote_note,
blockquote_tip,
blockquote_warning,
blockquote_important,
blockquote_caution {
  background-color: rgba(30, 144, 255, 0.06);
  border-left: 5px solid #1E90FF;
  padding: 15px 12px 15px 15px; /* 减小左右内边距 */
  margin: 1.8em 0;
  border-radius: 6px;
  position: relative;
  padding-top: 25px; /* 为标题腾出空间 */
}

blockquote_title {
  font-weight: 600;
  margin-bottom: 0.8em;
  position: absolute;
  top: 8px;
  left: 15px;
}

blockquote_title_note::before,
blockquote_title_tip::before,
blockquote_title_warning::before,
blockquote_title_important::before,
blockquote_title_caution::before {
  margin-right: 6px;
  font-weight: bold;
}

blockquote_title_note::before { content: "📝"; }
blockquote_title_tip::before { content: "💡"; }
blockquote_title_warning::before { content: "⚠️"; }
blockquote_title_important::before { content: "❗"; }
blockquote_title_caution::before { content: "🔔"; }

blockquote_title_note,
blockquote_title_tip,
blockquote_title_warning,
blockquote_title_important,
blockquote_title_caution {
  color: #1E90FF;
}

/* 新增: 卡片式设计 */
.card {
  background: #fff;
  border-radius: 10px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  padding: 15px; /* 减小内边距 */
  margin: 2em 0;
  border-top: 3px solid #1E90FF;
}

.card-title {
  font-size: 1.2em;
  font-weight: 600;
  margin-bottom: 1em;
  color: #1E90FF;
}

/* 新增: 步骤指引样式 */
.steps {
  counter-reset: step;
  margin: 2em 0;
}

.step-item {
  position: relative;
  padding-left: 45px;
  margin-bottom: 1.5em;
}

.step-item::before {
  counter-increment: step;
  content: counter(step);
  position: absolute;
  left: 0;
  top: 0;
  background: #1E90FF;
  color: white;
  width: 30px;
  height: 30px;
  border-radius: 50%;
  text-align: center;
  line-height: 30px;
  font-weight: bold;
}

.step-title {
  font-weight: 600;
  margin-bottom: 0.5em;
  color: #333;
}

/* 新增: 对比内容样式 */
.compare-container {
  display: flex;
  margin: 2em 0;
  flex-wrap: wrap; /* 移动端自动换行 */
}

.compare-item {
  flex: 1;
  min-width: 250px; /* 移动端最小宽度 */
  padding: 15px;
}

.compare-item.left {
  background-color: rgba(255, 0, 0, 0.05);
  border-radius: 6px 0 0 6px;
}

.compare-item.right {
  background-color: rgba(30, 144, 255, 0.05);
  border-radius: 0 6px 6px 0;
}

.compare-title {
  text-align: center;
  font-weight: 600;
  margin-bottom: 1em;
}

.compare-item.left .compare-title {
  color: #ff5252;
}

.compare-item.right .compare-title {
  color: #1E90FF;
}

/* 新增: 目录导航样式 */
.toc {
  background: rgba(30, 144, 255, 0.03);
  padding: 15px; /* 减小内边距 */
  border-radius: 8px;
  margin: 2em 0;
  border: 1px solid rgba(30, 144, 255, 0.2);
}

.toc-title {
  font-weight: 600;
  margin-bottom: 1em;
  color: #1E90FF;
  text-align: center;
}

.toc-list {
  padding-left: 1.5em;
}

.toc-item {
  margin-bottom: 0.8em;
}

.toc-item a {
  color: #444;
  text-decoration: none;
  border-bottom: none;
}

.toc-item a:hover {
  color: #1E90FF;
}

/* 新增: 文末引导区 */
.article-footer {
  margin-top: 3em;
  padding-top: 2em;
  border-top: 1px solid rgba(30, 144, 255, 0.2);
  text-align: center;
}

.footer-message {
  font-size: 1.1em;
  margin-bottom: 1.5em;
  color: #666;
}

.action-buttons {
  display: flex;
  justify-content: center;
  gap: 20px;
  margin-bottom: 2em;
}

.action-button {
  background: transparent;
  border: 1px solid #1E90FF;
  color: #1E90FF;
  padding: 8px 20px;
  border-radius: 20px;
  font-weight: 600;
  font-size: 0.9em;
}

.action-button.primary {
  background: #1E90FF;
  color: white;
}

/* 新增: 彩色标签 */
.tag {
  display: inline-block;
  padding: 3px 8px;
  border-radius: 4px;
  font-size: 0.85em;
  margin-right: 6px;
  margin-bottom: 6px;
  font-weight: 500;
}

.tag.blue {
  background-color: rgba(30, 144, 255, 0.15);
  color: #1E90FF;
}

.tag.green {
  background-color: rgba(76, 175, 80, 0.15);
  color: #4CAF50;
}

.tag.orange {
  background-color: rgba(255, 152, 0, 0.15);
  color: #FF9800;
}

.tag.red {
  background-color: rgba(244, 67, 54, 0.15);
  color: #F44336;
}

/* 优化移动端响应式 */
@media (max-width: 768px) {
  container {
    padding: 8px 2px; /* 恢复到v0.1的内边距设置 */
  }
  
  h1 {
    font-size: 1.6em;
  }
  
  h2 {
    font-size: 1.4em;
  }
  
  .compare-container {
    flex-direction: column;
  }
  
  .compare-item {
    border-radius: 6px;
    margin-bottom: 1em;
  }
  
  .action-buttons {
    flex-direction: column;
    gap: 10px;
  }
} 