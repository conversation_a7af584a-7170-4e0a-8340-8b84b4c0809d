/**
 * Miss利排版样式 v2.0 - 简化版 (20250712)
 * 品牌色: #1E90FF (深天蓝色)
 * 理念: 简洁自然，内容为王，去AI化设计
 */

/* 基础容器 - 移动端优化 */
.container {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Microsoft YaHei', sans-serif;
  line-height: 1.7;
  color: #333;
  padding: 15px;
  font-size: 16px;
  max-width: 100%;
  box-sizing: border-box;
}

/* 标题样式 - 简洁清晰 */
h1 {
  font-size: 1.6em;
  font-weight: 600;
  color: #1E90FF;
  margin: 1.5em 0 1em 0;
  line-height: 1.4;
}

h2 {
  font-size: 1.3em;
  font-weight: 600;
  color: #333;
  margin: 1.2em 0 0.8em 0;
  padding-left: 8px;
  border-left: 3px solid #1E90FF;
}

h3 {
  font-size: 1.1em;
  font-weight: 500;
  color: #555;
  margin: 1em 0 0.6em 0;
}

/* 段落样式 - 舒适阅读 */
p {
  margin: 0.8em 0;
  line-height: 1.7;
  text-align: justify;
}

/* 强调文本 */
strong {
  color: #1E90FF;
  font-weight: 600;
}

em {
  color: #666;
  font-style: normal;
  background: rgba(30, 144, 255, 0.1);
  padding: 2px 4px;
  border-radius: 3px;
}

/* 引用块 - 温暖感 */
blockquote {
  margin: 1.2em 0;
  padding: 15px 20px;
  background: rgba(30, 144, 255, 0.05);
  border-left: 4px solid #1E90FF;
  border-radius: 0 6px 6px 0;
  color: #555;
  font-style: normal;
}

blockquote p {
  margin: 0;
}

/* 列表样式 - 清晰易读 */
ul, ol {
  margin: 1em 0;
  padding-left: 1.5em;
}

li {
  margin: 0.4em 0;
  line-height: 1.6;
}

/* 代码样式 - 简洁实用 */
code {
  background: #f5f5f5;
  padding: 2px 6px;
  border-radius: 3px;
  font-family: 'Monaco', 'Consolas', monospace;
  font-size: 0.9em;
  color: #e74c3c;
}

pre {
  background: #f8f8f8;
  padding: 15px;
  border-radius: 6px;
  overflow-x: auto;
  margin: 1.2em 0;
  border: 1px solid #eee;
}

pre code {
  background: none;
  padding: 0;
  color: #333;
}

/* 链接样式 - 品牌色 */
a {
  color: #1E90FF;
  text-decoration: none;
  border-bottom: 1px solid transparent;
  transition: border-color 0.2s ease;
}

a:hover {
  border-bottom-color: #1E90FF;
}

/* 图片样式 - 自然展示 */
img {
  max-width: 100%;
  height: auto;
  border-radius: 6px;
  margin: 1em 0;
  display: block;
}

/* 表格样式 - 简洁清晰 */
table {
  width: 100%;
  border-collapse: collapse;
  margin: 1.2em 0;
  font-size: 0.95em;
}

th, td {
  padding: 10px 12px;
  text-align: left;
  border-bottom: 1px solid #eee;
}

th {
  background: rgba(30, 144, 255, 0.1);
  color: #1E90FF;
  font-weight: 600;
}

/* 分隔线 - 简单优雅 */
hr {
  border: none;
  height: 1px;
  background: linear-gradient(to right, transparent, #1E90FF, transparent);
  margin: 2em 0;
}

/* 特色模块 - Miss利专属 */
.teacher-tip {
  background: rgba(30, 144, 255, 0.08);
  border: 1px solid rgba(30, 144, 255, 0.2);
  border-radius: 8px;
  padding: 15px;
  margin: 1.2em 0;
}

.teacher-tip::before {
  content: "👩‍🏫 Miss利小贴士";
  display: block;
  color: #1E90FF;
  font-weight: 600;
  margin-bottom: 8px;
  font-size: 0.9em;
}

.student-voice {
  background: rgba(255, 193, 7, 0.1);
  border-left: 4px solid #ffc107;
  padding: 12px 15px;
  margin: 1em 0;
  border-radius: 0 6px 6px 0;
}

.student-voice::before {
  content: "🧒 学生说";
  display: block;
  color: #f39c12;
  font-weight: 600;
  margin-bottom: 5px;
  font-size: 0.85em;
}

/* 课堂案例框 */
.classroom-case {
  background: rgba(46, 204, 113, 0.1);
  border: 1px dashed rgba(46, 204, 113, 0.3);
  border-radius: 8px;
  padding: 15px;
  margin: 1.2em 0;
}

.classroom-case::before {
  content: "📚 课堂案例";
  display: block;
  color: #27ae60;
  font-weight: 600;
  margin-bottom: 8px;
  font-size: 0.9em;
}

/* 移动端优化 */
@media (max-width: 768px) {
  .container {
    padding: 12px;
    font-size: 15px;
  }
  
  h1 {
    font-size: 1.4em;
  }
  
  h2 {
    font-size: 1.2em;
  }
  
  h3 {
    font-size: 1.05em;
  }
  
  blockquote {
    padding: 12px 15px;
    margin: 1em 0;
  }
  
  pre {
    padding: 12px;
    font-size: 0.85em;
  }
  
  table {
    font-size: 0.9em;
  }
  
  th, td {
    padding: 8px 10px;
  }
  
  .teacher-tip,
  .classroom-case {
    padding: 12px;
  }
}

/* 打印样式 - 简洁实用 */
@media print {
  .container {
    padding: 0;
    font-size: 12pt;
    line-height: 1.5;
  }
  
  h1, h2, h3 {
    color: #000;
    page-break-after: avoid;
  }
  
  blockquote {
    border-left: 2px solid #000;
    background: none;
  }
  
  .teacher-tip,
  .student-voice,
  .classroom-case {
    border: 1px solid #ccc;
    background: none;
  }
  
  a {
    color: #000;
    text-decoration: underline;
  }
}
