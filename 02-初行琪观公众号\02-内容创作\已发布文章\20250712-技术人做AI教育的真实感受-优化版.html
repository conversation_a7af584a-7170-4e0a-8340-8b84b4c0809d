<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>初行琪观 - 重构人工智能教学平台的踩坑实录</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@400;500;700&family=Inter:wght@600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@400;500;700&family=Inter:wght@600;700&display=swap');
        
        body {
            font-family: 'Noto Sans SC', sans-serif;
            background-color: #f5f5f5;
            padding: 20px;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
        }
        
        .container {
            width: 100%;
            max-width: 1200px;
            aspect-ratio: 3.35/1;
            display: flex;
            background-color: white;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            position: relative;
            overflow: hidden;
        }
        
        .main-cover {
            flex: 2.35;
            background-color: #079452;
            position: relative;
            padding: 40px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            overflow: hidden;
        }
        
        .wechat-cover {
            flex: 1;
            aspect-ratio: 1/1;
            background-color: #079452;
            padding: 30px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            position: relative;
            overflow: hidden;
        }
        
        .title {
            font-family: 'Inter', 'Noto Sans SC', sans-serif;
            font-weight: 700;
            font-size: 3.5vw;
            color: white;
            line-height: 1.2;
            margin-bottom: 20px;
            position: relative;
            z-index: 2;
        }
        
        .title span.highlight {
            color: #FFD966;
        }
        
        .subtitle {
            font-size: 1.2vw;
            color: rgba(255, 255, 255, 0.9);
            line-height: 1.6;
            margin-bottom: 30px;
            position: relative;
            z-index: 2;
        }
        
        .tags {
            display: flex;
            gap: 10px;
            position: relative;
            z-index: 2;
        }
        
        .tag {
            background-color: rgba(255, 217, 102, 0.2);
            color: #FFD966;
            padding: 5px 15px;
            border-radius: 20px;
            font-size: 0.9vw;
            font-weight: 500;
        }
        
        .tech-elements {
            position: absolute;
            width: 100%;
            height: 100%;
            top: 0;
            left: 0;
            opacity: 0.1;
            z-index: 1;
        }
        
        .circuit {
            position: absolute;
            width: 80%;
            height: 80%;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background-image: 
                linear-gradient(to right, rgba(255, 255, 255, 0.3) 1px, transparent 1px),
                linear-gradient(to bottom, rgba(255, 255, 255, 0.3) 1px, transparent 1px);
            background-size: 30px 30px;
        }
        
        .ai-icon {
            position: absolute;
            font-size: 2vw;
            color: rgba(255, 217, 102, 0.3);
        }
        
        @media (max-width: 768px) {
            .container {
                flex-direction: column;
                aspect-ratio: unset;
            }
            
            .main-cover, .wechat-cover {
                flex: none;
                width: 100%;
                aspect-ratio: unset;
            }
            
            .title {
                font-size: 24px;
            }
            
            .subtitle {
                font-size: 14px;
            }
            
            .tag {
                font-size: 10px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 主封面区域 -->
        <div class="main-cover">
            <div class="tech-elements">
                <div class="circuit"></div>
                <i class="ai-icon fas fa-robot" style="top: 20%; left: 70%;"></i>
                <i class="ai-icon fas fa-brain" style="top: 60%; left: 20%;"></i>
                <i class="ai-icon fas fa-project-diagram" style="top: 30%; left: 30%;"></i>
            </div>
            <h1 class="title">重构人工智能教育教学平台的<span class="highlight">踩坑实录</span></h1>
            <p class="subtitle">从架构设计到落地实践，技术选型与性能优化，避坑指南与最佳实践</p>
            <div class="tags">
                <div class="tag">人工智能教育</div>
                <div class="tag">系统重构</div>
                <div class="tag">技术实践</div>
                <div class="tag">避坑指南</div>
            </div>
        </div>
        
        <!-- 朋友圈封面区域 -->
        <div class="wechat-cover">
            <div class="tech-elements">
                <div class="circuit"></div>
                <i class="ai-icon fas fa-robot" style="top: 15%; left: 50%; transform: translateX(-50%); font-size: 2rem;"></i>
            </div>
            <h1 class="title" style="font-size: 2rem; text-align: center; margin-bottom: 15px;">重构人工智能教育教学平台的<br><span class="highlight">踩坑实录</span></h1>
            <p class="subtitle" style="font-size: 1rem; text-align: center; margin-bottom: 20px;">技术选型与性能优化</p>
            <div class="tags" style="margin-top: 10px;">
                <div class="tag" style="font-size: 0.8rem; padding: 4px 12px;">人工智能教育</div>
                <div class="tag" style="font-size: 0.8rem; padding: 4px 12px;">避坑指南</div>
            </div>
        </div>
    </div>
</body>
</html>