/**
 * Miss 利 - 专业科技教育主题 (修正版 v2)
 * 主题色: #1E90FF (深天蓝色)
 * 特点: 清晰、专业、科技感、教育温度
 * 修正: 确保 h2 样式正确，并尝试修正深色背景元素的文字颜色
 */

/* 顶层容器样式 - 设置基础字体和行高 */
container {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', 'Microsoft YaHei', sans-serif; /* 优先使用系统现代字体 */
  line-height: 1.75; /* 舒适的行高 */
  color: #333333; /* 主要文字颜色，深灰，保证高对比度 */
  padding: 10px 5px; /* 轻微调整内容区内边距 */
}

/* 一级标题样式 - 突出、与主题色关联 */
h1 {
  color: #1E90FF; /* 使用主题色 */
  font-size: 1.6em; /* 稍大字号 */
  font-weight: 600; /* 加粗 */
  border-bottom: 2px solid #1E90FF; /* 主题色下划线，强化标题 */
  padding-bottom: 0.4em;
  margin-top: 1.8em;
  margin-bottom: 1em;
}

/* 二级标题样式 - 清晰、结构化 (这是正确的H2样式) */
h2 {
  color: #2c3e50; /* 深蓝灰色文字，保证对比度 */
  font-size: 1.4em;
  font-weight: 600;
  margin-top: 1.6em;
  margin-bottom: 0.8em;
  border-left: 4px solid #1E90FF; /* 左侧加主题色边框，突出结构 */
  padding-left: 10px;
  background-color: transparent; /* 确保没有背景色 */
}

/* 三级标题样式 - 次要层级 */
h3 {
  color: #34495e; /* 稍浅的蓝灰色 */
  font-size: 1.2em;
  font-weight: 600;
  margin-top: 1.4em;
  margin-bottom: 0.6em;
}

/* 段落样式 - 保证可读性 */
p {
  margin-bottom: 1.2em; /* 段后距 */
  letter-spacing: 0.5px; /* 轻微增加字间距，提升阅读感 */
}

/* 图片样式 - 可选：加点圆角和阴影增加质感 */
image {
  border-radius: 6px; /* 轻微圆角 */
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1); /* 轻微阴影 */
  margin-top: 1em;
  margin-bottom: 1em;
  display: block; /* 避免图片下有多余空隙 */
  max-width: 100%; /* 确保图片不超出边界 */
}

/* 引用样式 - 用于引言、强调、思考点 */
blockquote {
  border-left: 4px solid #1E90FF; /* 主题色左边框 */
  background-color: rgba(30, 144, 255, 0.05); /* 非常淡的主题色背景 */
  color: #555555; /* 引用文字颜色 */
  padding: 12px 18px;
  margin: 1.5em 0;
  border-radius: 0 4px 4px 0; /* 右上和右下轻微圆角 */
}

/* 引用段落样式 */
blockquote_p {
  margin-bottom: 0.5em; /* 引用内段落间距可小点 */
}

/* 分割线样式 */
hr {
  border: 0;
  height: 1px;
  background-image: linear-gradient(to right, rgba(30, 144, 255, 0), rgba(30, 144, 255, 0.75), rgba(30, 144, 255, 0)); /* 用主题色渐变做分割线 */
  margin: 2em 0;
}

/* 行内代码样式 - 突出代码或术语 */
codespan {
  background-color: rgba(30, 144, 255, 0.1); /* 淡主题色背景 */
  color: #c7254e; /* 沿用常见代码颜色，或用 #1E90FF */
  padding: .2em .4em;
  margin: 0 2px;
  border-radius: 3px;
  font-size: 0.9em;
  font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, Courier, monospace;
}

/* 粗体样式 - 用于强调 */
strong {
  color: #1E90FF; /* 强调文字使用主题色 */
  font-weight: 600;
}

/* 链接样式 */
link, wx_link {
  color: #1E90FF; /* 链接使用主题色 */
  text-decoration: none; /* 去掉默认下划线 */
  border-bottom: 1px dotted #1E90FF; /* 用虚线或点状下划线替代 */
  padding-bottom: 1px;
}
link:hover, wx_link:hover {
  color: #007acc; /* 鼠标悬停时颜色加深 */
  border-bottom: 1px solid #007acc; /* 悬停时变实线 */
}

/* 列表样式 - 优化间距 */
ul, ol {
  padding-left: 1.8em; /* 列表缩进 */
  margin-bottom: 1em;
}
listitem {
  margin-bottom: 0.5em; /* 列表项之间的间距 */
}

/* 代码块样式 - AI工具指南和技术教程的核心 */
code_pre {
  background-color: #f8f9fa; /* 非常浅的灰色背景 */
  border: 1px solid #dee2e6; /* 细边框 */
  padding: 1em;
  margin: 1.5em 0;
  border-radius: 5px; /* 圆角 */
  overflow-x: auto; /* 代码过长时允许横向滚动 */
  font-size: 0.95em; /* 代码字号可略小于正文 */
}
code {
  font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, Courier, monospace; /* 使用等宽字体 */
  color: #333; /* 代码文字基础颜色 */
  /* 代码高亮由编辑器自带JS处理，这里只设定基础样式 */
}

/* --- GFM 提示框样式 --- */
blockquote_note { background-color: rgba(30, 144, 255, 0.08); border-left: 5px solid #1E90FF; padding: 12px 20px; margin: 1.5em 0; border-radius: 4px; }
blockquote_tip { background-color: rgba(40, 167, 69, 0.08); border-left: 5px solid #28a745; padding: 12px 20px; margin: 1.5em 0; border-radius: 4px; }
blockquote_warning { background-color: rgba(255, 193, 7, 0.08); border-left: 5px solid #ffc107; padding: 12px 20px; margin: 1.5em 0; border-radius: 4px; }
blockquote_important, blockquote_caution { background-color: rgba(220, 53, 69, 0.08); border-left: 5px solid #dc3545; padding: 12px 20px; margin: 1.5em 0; border-radius: 4px; }
blockquote_title { font-weight: 600; margin-bottom: 0.5em; }
blockquote_title_note { color: #1E90FF; }
blockquote_title_tip { color: #28a745; }
blockquote_title_warning { color: #ffc107; }
blockquote_title_important, blockquote_title_caution { color: #dc3545; }

/* --- Katex 公式样式 (如果需要) --- */
/* inline_katex {} */
/* block_katex {} */


/* --- 修正尝试：处理深色背景配浅色文字的问题 --- */
/*
 *  注意：以下规则是基于猜测，试图修复截图中看到的“蓝底黑字”元素。
 *  你需要使用浏览器“检查元素”功能找到该元素的准确 HTML 标签和 CSS 类名，
 *  然后用准确的选择器替换下面的示例选择器 (如 .button-style, a.special-link 等)。
 *  如果找到了准确的选择器，可以尝试去掉 !important。
*/

/* 示例选择器，你需要替换成真实的 */
.button-style-example, /* 假设它有一个叫 button-style-example 的类 */
a.button-like-link,   /* 假设它是一个带 button-like-link 类的链接 */
div.special-highlight  /* 假设它是一个带 special-highlight 类的 div */
{
  /* 如果这些元素的背景色恰好是你的主题色 */
  /* 强制将文字颜色设为白色以保证对比度 */
  background-color: #1E90FF; /* 保留或确认背景色 */
  color: #FFFFFF !important; /* 强制文字为白色！*/

  /* 可以根据需要添加其他按钮样式 */
  padding: 8px 15px;
  border-radius: 20px; /* 胶囊状圆角 */
  text-align: center;
  display: inline-block; /* 让它像按钮一样 */
  border: none; /* 可能需要去掉边框 */
}

/* 也可以尝试更通用的规则，但有风险影响其他元素 */
/* 例如：所有背景色是 #1E90FF 的元素，文字都变白 */
/* [style*="background-color: #1E90FF"], [style*="background: #1E90FF"] { */
/*   color: #FFFFFF !important; */
/* } */
/* 上面这条基于 style 属性的规则通常不推荐，且不一定有效，仅作备选思路 */