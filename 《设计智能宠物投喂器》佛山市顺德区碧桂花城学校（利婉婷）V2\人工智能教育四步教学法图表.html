<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>人工智能教育四步教学法图表</title>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            display: flex;
            flex-direction: column;
            align-items: center;
            background-color: #f5f7fa;
            padding: 20px;
        }
        .container {
            position: relative;
            width: 900px;
            margin-bottom: 20px;
        }
        .teaching-path {
            background-color: white;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            padding: 30px;
            margin-bottom: 20px;
        }
        .title {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 30px;
            font-size: 24px;
            font-weight: bold;
        }
        .steps-container {
            display: flex;
            justify-content: space-between;
            position: relative;
            padding: 0 20px;
        }
        .steps-container:before {
            content: "";
            position: absolute;
            top: 60px;
            left: 50px;
            right: 50px;
            height: 4px;
            background: linear-gradient(90deg, #3498db, #9b59b6);
            z-index: 1;
        }
        .step {
            width: 23%;
            text-align: center;
            position: relative;
            z-index: 2;
            padding: 0 10px;
        }
        .step-icon {
            width: 80px;
            height: 80px;
            background: white;
            border-radius: 50%;
            margin: 0 auto 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            border: 3px solid #fff;
            font-size: 32px;
        }
        .step-1 .step-icon { background-color: #f1c40f; }
        .step-2 .step-icon { background-color: #2ecc71; }
        .step-3 .step-icon { background-color: #e74c3c; }
        .step-4 .step-icon { background-color: #9b59b6; }
        
        .step-title {
            font-weight: bold;
            margin-bottom: 10px;
            color: #2c3e50;
            font-size: 18px;
        }
        .step-subtitle {
            font-size: 16px;
            color: #7f8c8d;
            font-style: italic;
            margin-bottom: 8px;
        }
        .step-desc {
            font-size: 15px;
            color: #34495e;
            line-height: 1.6;
            min-height: 100px;
        }
        .download-btn {
            background-color: #3498db;
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            transition: all 0.3s;
            display: flex;
            align-items: center;
            margin: 30px auto 0;
            box-shadow: 0 3px 10px rgba(52,152,219,0.3);
        }
        .download-btn:hover {
            background-color: #2980b9;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(52,152,219,0.4);
        }
        .download-btn svg {
            margin-right: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="teaching-path" id="infographic">
            <div class="title">人工智能教育四步教学法</div>
            
            <div class="steps-container">
                <!-- 第一步 -->
                <div class="step step-1">
                    <div class="step-icon">🔍</div>
                    <div class="step-title">问题分析</div>
                    <div class="step-subtitle">选材备料</div>
                    <div class="step-desc">
                        • 观察宠物独处行为<br>
                        • 分类整理需求清单<br>
                        • 明确功能指标
                    </div>
                </div>
                
                <!-- 第二步 -->
                <div class="step step-2">
                    <div class="step-icon">📝</div>
                    <div class="step-title">方案设计</div>
                    <div class="step-subtitle">研究菜谱</div>
                    <div class="step-desc">
                        • 绘制系统流程图<br>
                        • 学习OpenInnoLab平台基础操作<br>
                        • 确定核心功能
                    </div>
                </div>
                
                <!-- 第三步 -->
                <div class="step step-3">
                    <div class="step-icon">👩‍🍳</div>
                    <div class="step-title">实施验证</div>
                    <div class="step-subtitle">掌勺烹饪</div>
                    <div class="step-desc">
                        • 概念认知<br>
                        • 模型训练<br>
                        • 现象探究
                    </div>
                </div>
                
                <!-- 第四步 -->
                <div class="step step-4">
                    <div class="step-icon">✨</div>
                    <div class="step-title">优化迭代</div>
                    <div class="step-subtitle">调味改进</div>
                    <div class="step-desc">
                        • 增加犬种样本多样性<br>
                        • 添加环境降噪模块<br>
                        • 按体型调整投喂量
                    </div>
                </div>
            </div>
        </div>
    </div>

    <button class="download-btn" id="downloadBtn">
        <svg width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
            <polyline points="7 10 12 15 17 10"></polyline>
            <line x1="12" y1="15" x2="12" y2="3"></line>
        </svg>
        下载教学流程图
    </button>

    <script src="https://html2canvas.hertzen.com/dist/html2canvas.min.js"></script>
    <script>
        document.getElementById('downloadBtn').addEventListener('click', function() {
            const element = document.getElementById('infographic');
            const originalWidth = element.offsetWidth;
            const originalHeight = element.offsetHeight;
            const scale = 2;
            
            html2canvas(element, {
                backgroundColor: null,
                scale: scale,
                width: originalWidth,
                height: originalHeight,
                windowWidth: originalWidth * scale,
                windowHeight: originalHeight * scale
            }).then(canvas => {
                const link = document.createElement('a');
                link.download = '人工智能教育四步教学法.png';
                link.href = canvas.toDataURL('image/png');
                link.click();
            });
        });
    </script>
</body>
</html>