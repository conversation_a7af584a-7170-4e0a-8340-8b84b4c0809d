<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Miss利个人资料卡</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@5.15.4/css/all.min.css" rel="stylesheet">
    <style>
        body { 
            background-color: #f0f8ff;
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 20px;
        }
        .card { 
            background: linear-gradient(180deg, #7ac2ff 0%, #f4efef 85%, rgba(244, 239, 239, 0.8) 100%);
            box-shadow: 0 15px 30px rgba(122, 194, 255, 0.2);
            border-radius: 24px;
            max-width: 500px;
            width: 100%;
            margin: auto;
        }
        .section { 
            background-color: rgba(255, 255, 255, 0.85);
            border-radius: 16px;
            padding: 16px;
            margin-bottom: 16px;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(122, 194, 255, 0.08);
            border: 1px solid rgba(255, 255, 255, 0.9);
        }
        .section:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(122, 194, 255, 0.1);
        }
        .expertise-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 16px;
        }
        .expertise-item { 
            background: linear-gradient(145deg, #ffffff, #f8f9fa);
            border-radius: 12px;
            padding: 16px;
            transition: all 0.3s ease;
            border: 1px solid rgba(122, 194, 255, 0.15);
            box-shadow: 0 2px 8px rgba(122, 194, 255, 0.06);
        }
        .expertise-item:hover {
            transform: translateY(-2px);
            background-color: rgba(255, 255, 255, 0.9);
        }
        .interest-tag { 
            background: linear-gradient(135deg, #7ac2ff, #6ab0eb);
            color: white;
            padding: 6px 12px;
            border-radius: 20px;
            display: inline-block;
            margin: 4px;
            transition: all 0.3s ease;
            box-shadow: 0 2px 6px rgba(122, 194, 255, 0.15);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        .interest-tag:hover {
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(122, 194, 255, 0.2);
        }
        .content-section {
            padding: 24px;
        }
        .qr-section {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 20px;
            padding-top: 20px;
        }
        .qr-code {
            width: 100px;
            height: 100px;
            background: white;
            border-radius: 12px;
            padding: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
        }
        .text-primary { color: #4a90e2; font-weight: 600; }
        .text-content { color: #2c3e50; }
        .header-content {
            padding: 24px;
        }
        .qr-section .text-primary {
            color: rgba(74, 144, 226, 0.9);
        }
        .slogan {
            color: rgba(74, 144, 226, 0.95);
            font-size: 1.5rem;
            font-weight: 600;
            text-shadow: 0 1px 2px rgba(255, 255, 255, 0.8);
        }
    </style>
</head>
<body>
    <div class="card">
        <!-- 头部信息 -->
        <div class="header-content">
            <div class="flex items-center">
                <img src="https:/img.ctnas.com/ctnas-img/2024/11/ce66b915460b812f4342cf78314260bc.202411241640299.png" 
                     alt="Profile" 
                     class="w-20 h-20 rounded-full border-2 border-white shadow-md object-cover">
                <div class="ml-5">
                    <h2 class="text-2xl font-bold text-white mb-1">Miss利</h2>
                    <p class="flex items-center text-white mb-1">
                        <i class="fas fa-map-marker-alt mr-2"></i>顺德
                    </p>
                    <p class="text-white">信息科技教师 | 教育+AI探索者 | Maggie妈妈</p>
                </div>
            </div>
        </div>

        <div class="content-section">
            <!-- 近期关注 -->
            <div class="section">
                <h3 class="text-lg font-semibold text-primary flex items-center mb-3">
                    <i class="fas fa-bullseye mr-3"></i>近期关注
                </h3>
                 <ul class="text-gray-700 space-y-2">
                       <li>信息科技教育创新：教学、竞赛指导和教育+AI探索</li>
                       <li>培训编程、创客、模型等竞赛，提升孩子们科技实践能力。</li>
                       <li>创办"AI创新探索营"；打造"Miss利@魔法画笔乐园"AI绘画教学平台，用AI激发孩子们创造力。</li>
                 </ul>
            </div>

            <!-- 职业亮点 -->
            <div class="section">
                <h3 class="text-lg font-semibold text-primary flex items-center mb-3">
                    <i class="fas fa-award mr-3"></i>职业亮点
                </h3>
                <ul class="text-gray-700 pl-5 list-disc">
                    <li>深耕信息科技教育创新，打造AI+教学特色课程体系</li>
                    <li>运用创新教学法辅导孩子们在全国、省、市级信息科技大赛中屡获佳绩</li>
                    <li>多次荣获"优秀辅导教师"、"金牌教练"等称号</li>
                </ul>
            </div>

            <!-- 专长领域 -->
            <div class="section">
                <h3 class="text-lg font-semibold text-primary flex items-center mb-3">
                    <i class="fas fa-bolt mr-3"></i>专长领域
                </h3>
                <div class="expertise-grid">
                    <div class="expertise-item">
                        <h4 class="font-semibold text-primary mb-1">信息科技教育</h4>
                        <p class="text-gray-600 text-sm">十三年教育耕耘，培养孩子们科技创新思维</p>
                    </div>
                    <div class="expertise-item">
                        <h4 class="font-semibold text-primary mb-1">科技创新竞赛</h4>
                        <p class="text-gray-600 text-sm">提供个性化竞赛指导，激发孩子们创造潜能</p>
                    </div>
                    <div class="expertise-item">
                        <h4 class="font-semibold text-primary mb-1">教育+AI探索</h4>
                        <p class="text-gray-600 text-sm">将AI技术融入课堂教学，设计趣味化智慧学习场景</p>
                    </div>
                    <div class="expertise-item">
                        <h4 class="font-semibold text-primary mb-1">AI创意教育</h4>
                        <p class="text-gray-600 text-sm">“AI创新探索营”，引导孩子们人工智能创作</p>
                    </div>
                </div>
            </div>

            <!-- 兴趣爱好 -->
            <div class="section">
                <h3 class="text-lg font-semibold text-primary flex items-center mb-3">
                    <i class="fas fa-heart mr-3"></i>兴趣爱好
                </h3>
                <div>
                    <span class="interest-tag">📚 教育创新</span>
                    <span class="interest-tag">💻 科技探索</span>
                    <span class="interest-tag">💖 亲子成长</span> 
                </div>
            </div>

            <!-- 页脚 -->
            <div class="qr-section">
                <div>
                    <div class="text-primary text-lg mb-2">
                        <i class="fas fa-qrcode mr-2"></i>扫码"Miss利AI研习笔记"了解更多
                    </div>
                     <p class="text-primary text-3xl font-semibold">
                               用科技点亮童年梦想
                     </p>
                </div>
                <img src="https:/img.ctnas.com/ctnas-img/2024/11/7603bff8fb070d259b248df75a78b312.202411241640083.jpg" 
                     alt="QR Code" 
                     class="qr-code">
            </div>
        </div>
    </div>
</body>
</html>