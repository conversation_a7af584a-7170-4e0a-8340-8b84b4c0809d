<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Miss利个人资料卡</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@5.15.4/css/all.min.css" rel="stylesheet">
    <style>
        body { 
            background-color: #f0f8ff;
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: flex-start; 
            padding: 20px;
            overflow-y: auto; 
        }
        .card { 
            background: linear-gradient(180deg, #7ac2ff 0%, #f4efef 85%, rgba(244, 239, 239, 0.8) 100%);
            box-shadow: 0 15px 30px rgba(122, 194, 255, 0.2);
            border-radius: 24px;
            max-width: 500px;
            width: 100%;
            margin: auto;
        }
        .section { 
            background-color: rgba(230, 245, 255, 0.6); 
            border-radius: 16px;
            padding: 16px; 
            margin-bottom: 16px;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(122, 194, 255, 0.08);
            border: 1px solid rgba(122, 194, 255, 0.1);
        }
        .section:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(122, 194, 255, 0.12);
        }
        .expertise-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(180px, 1fr)); 
            gap: 16px;
        }
        .expertise-item { 
            background: linear-gradient(145deg, #ffffff, #f8f9fa);
            border-radius: 12px;
            padding: 16px;
            transition: all 0.3s ease;
            border: 1px solid rgba(122, 194, 255, 0.15);
            box-shadow: 0 2px 8px rgba(122, 194, 255, 0.06);
        }
        .expertise-item:hover {
            transform: translateY(-2px);
            background-color: rgba(255, 255, 255, 0.9);
        }
        
        .interest-tag-edu { 
            background-color: #FADADD; 
            color: #555555; 
            padding: 6px 12px; 
            border-radius: 20px;
            display: inline-block;
            margin: 4px;
            transition: all 0.3s ease;
            box-shadow: 0 2px 6px rgba(250, 218, 221, 0.5);
            border: 1px solid rgba(250, 218, 221, 0.8);
            font-size: 0.9rem; 
        }
        .interest-tag-tech { 
            background-color: #E0E7FF; 
            color: #555555;
            padding: 6px 12px;
            border-radius: 20px;
            display: inline-block;
            margin: 4px;
            transition: all 0.3s ease;
            box-shadow: 0 2px 6px rgba(224, 231, 255, 0.5);
            border: 1px solid rgba(224, 231, 255, 0.8);
            font-size: 0.9rem;
        }
        .interest-tag-family { 
            background-color: #FFF5E1; 
            color: #555555;
            padding: 6px 12px;
            border-radius: 20px;
            display: inline-block;
            margin: 4px;
            transition: all 0.3s ease;
            box-shadow: 0 2px 6px rgba(255, 245, 225, 0.5);
            border: 1px solid rgba(255, 245, 225, 0.8);
            font-size: 0.9rem;
        }
        .interest-tag-edu:hover, .interest-tag-tech:hover, .interest-tag-family:hover {
            transform: translateY(-1px);
            filter: brightness(1.1);
            box-shadow: 0 3px 7px rgba(0,0,0,0.1);
        }

        .content-section {
            padding: 20px; 
        }
        .qr-section {
            display: flex;
            flex-direction: column; 
            align-items: center;
            text-align: center;
            margin-top: 20px;
            padding-top: 20px;
        }
        .qr-code {
            width: 120px; 
            height: 120px;
            background: white;
            border-radius: 12px;
            padding: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
            margin-bottom: 10px; 
        }
        .text-primary { color: #4a90e2; font-weight: 600; }
        .text-content { color: #2c3e50; } 
        .header-content {
            padding: 24px;
        }
        .qr-section .text-primary {
            color: rgba(74, 144, 226, 0.9);
        }
        .slogan { 
            color: rgba(74, 144, 226, 0.95);
            font-size: 1.1rem; 
            font-weight: 600;
            text-shadow: 0 1px 2px rgba(255, 255, 255, 0.8);
        }
        .section-title-icon { 
            margin-right: 10px; 
            font-size: 1.1em; 
        }
        .quote-style-education { 
            border-left: 3px solid #4a90e2; 
            padding-left: 0.75rem; 
            margin-left: 0.25rem;
        }
        .download-button {
            background-color: #4a90e2;
            color: white;
            padding: 10px 20px;
            border-radius: 8px;
            text-align: center;
            cursor: pointer;
            font-weight: 600;
            margin-top: 15px;
            transition: background-color 0.3s ease;
            border: none;
        }
        .download-button:hover {
            background-color: #357abd;
        }
    </style>
</head>
<body>
    <div class="card" id="missliCard">
        <!-- 头部信息 -->
        <div class="header-content">
            <div class="flex items-center">
                <img src="https://img.ctnas.com/ctnas-img/2024/11/ce66b915460b812f4342cf78314260bc.202411241640299.png" 
                     alt="Profile" 
                     class="w-20 h-20 rounded-full border-2 border-white shadow-md object-cover">
                <div class="ml-5">
                    <h2 class="text-2xl font-bold text-white mb-1">Miss利</h2>
                    <p class="flex items-center text-white mb-1 text-sm">
                        <i class="fas fa-map-marker-alt mr-2"></i>顺德
                    </p>
                    <p class="text-white text-sm leading-tight">信息科技教师 | AI教与学探索与分享者 | Maggie妈妈</p>
                </div>
            </div>
        </div>

        <div class="content-section">
            <!-- 近期关注 -->
            <div class="section">
                <h3 class="text-lg font-semibold text-primary flex items-center mb-2">
                    <i class="fas fa-bullseye section-title-icon"></i>近期关注
                </h3>
                 <ul class="text-gray-700 pl-5 list-disc space-y-1 text-base leading-relaxed">
                    <li>教育创新：深耕AI+教学融合，探索有温度的科技教育实践</li>
                    <li>竞赛培养：指导创客、模型、编程、人工智能等科创赛事，挖掘孩子们的科技潜能</li>
                    <li>特色项目：创建"AI创新探索营"与"AI人工智能社团"，搭建"Miss利@魔法画笔乐园"教学平台，点亮学生创造力</li>
                 </ul>
            </div>

            <!-- 职业亮点 -->
            <div class="section">
                <h3 class="text-lg font-semibold text-primary flex items-center mb-2">
                    <i class="fas fa-award section-title-icon"></i>职业亮点
                </h3>
                <ul class="text-gray-700 pl-5 list-disc space-y-1 text-base leading-relaxed">
                    <li>创新课程：研发AI+教学特色课程体系，获教育部专家高度评价</li>
                    <li>赛事成就：指导学生团队在国家级信息科技赛事中屡获金奖</li>
                    <li>专业荣誉：多次荣获"优秀辅导教师"、"金牌教练"等称号</li>
                </ul>
            </div>

            <!-- 专长领域 -->
            <div class="section">
                <h3 class="text-lg font-semibold text-primary flex items-center mb-2">
                    <i class="fas fa-bolt section-title-icon"></i>专长领域
                </h3>
                <div class="expertise-grid">
                    <div class="expertise-item">
                        <h4 class="font-semibold text-primary mb-1 text-base">信息科技教育</h4>
                        <p class="text-gray-600 text-sm leading-relaxed">十四载教学历程，构建学生计算思维与数字素养体系</p>
                    </div>
                    <div class="expertise-item">
                        <h4 class="font-semibold text-primary mb-1 text-base">科技创新竞赛</h4>
                        <p class="text-gray-600 text-sm leading-relaxed">个性化竞赛培训，助学生在国家级赛事中屡创佳绩</p>
                    </div>
                    <div class="expertise-item">
                        <h4 class="font-semibold text-primary mb-1 text-base">人工智能教育探索</h4>
                        <p class="text-gray-600 text-sm leading-relaxed">将AI技术有温度地融入课堂，创设趣味化智慧学习环境</p>
                    </div>
                    <div class="expertise-item">
                        <h4 class="font-semibold text-primary mb-1 text-base">AI创意教育</h4>
                        <p class="text-gray-600 text-sm leading-relaxed">"AI创新探索营"特色项目，培养技术创造者而非简单使用者</p>
                    </div>
                </div>
            </div>

            <!-- 教育理念 -->
            <div class="section">
                <h3 class="text-lg font-semibold text-primary flex items-center mb-2">
                    <i class="fas fa-lightbulb section-title-icon"></i>教育理念
                </h3>
                <p class="text-gray-700 text-base leading-relaxed quote-style-education">
                    用有温度的科技教育，点亮每个孩子的独特闪光点
                </p>
            </div>

            <!-- 兴趣爱好 -->
            <div class="section">
                <h3 class="text-lg font-semibold text-primary flex items-center mb-3">
                    <i class="fas fa-heart section-title-icon"></i>兴趣爱好
                </h3>
                <div class="text-center"> 
                    <span class="interest-tag-edu">📚 教育创新</span>
                    <span class="interest-tag-tech">💻 科技探索</span>
                    <span class="interest-tag-family">💖 亲子成长</span> 
                </div>
            </div>

            <!-- 页脚 -->
            <div class="qr-section section"> 
                 <img src="https://img.ctnas.com/ctnas-img/2024/12/1ac4bc34b4a065e6d7cce27e9001a9ed.202412022259113.png" 
                     alt="QR Code" 
                     class="qr-code">
                <div>
                    <p class="text-primary text-sm mb-1"> 
                        <i class="fas fa-qrcode mr-1"></i>扫码关注"Miss利AI研习笔记"
                    </p>
                     <p class="slogan">
                        分享AI实践 · 赋能智慧教育
                     </p>
                </div>
            </div>
        </div>
    </div>

</body>
</html>