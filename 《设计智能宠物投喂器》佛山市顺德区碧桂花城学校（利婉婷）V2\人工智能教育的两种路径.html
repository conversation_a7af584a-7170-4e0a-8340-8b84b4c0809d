<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>人工智能教育路径对比</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background-color: #ffffff;
            color: #333;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            display: flex;
            flex-direction: column;
            align-items: center;
        }
        .comparison-container {
            background-color: white;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            padding: 30px;
            margin-bottom: 20px;
            width: 100%;
            max-width: 800px;
        }
        h2 {
            text-align: center;
            color: #2c3e50;
            margin-bottom: 5px;
            font-size: 24px;
        }
        .subtitle {
            text-align: center;
            color: #7f8c8d;
            font-size: 16px;
            font-style: italic;
            margin-bottom: 10px;
        }
        .tag-container {
            text-align: center;
            margin-bottom: 30px;
        }
        .tag {
            display: inline-block;
            background-color: #3498db;
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-weight: bold;
            font-size: 18px;
            box-shadow: 0 3px 10px rgba(52,152,219,0.3);
        }
        .paths-comparison {
            display: flex;
            gap: 25px;
            margin-bottom: 30px;
        }
        .path {
            flex: 1;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            position: relative;
        }
        .path-header {
            padding: 15px;
            color: white;
            font-weight: bold;
            font-size: 16px;
            text-align: center;
        }
        .situational-header {
            background-color: #27ae60;
        }
        .exploratory-header {
            background-color: #8e44ad;
        }
        .path-content {
            padding: 20px;
            background-color: white;
        }
        .path-diagram {
            height: 220px;
            position: relative;
            margin-bottom: 20px;
        }
        .path-step {
            position: absolute;
            width: 120px;
            height: 60px;
            background-color: white;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            text-align: center;
            font-size: 14px;
            box-shadow: 0 3px 10px rgba(0,0,0,0.1);
            z-index: 2;
        }
        .sit-step-1 {
            top: 20px;
            left: 10px;
            background-color: #eafaf1;
            border: 1px solid #27ae60;
        }
        .sit-step-2 {
            top: 80px;
            left: 50%;
            transform: translateX(-50%);
            background-color: #eafaf1;
            border: 1px solid #27ae60;
        }
        .sit-step-3 {
            bottom: 20px;
            right: 10px;
            background-color: #eafaf1;
            border: 1px solid #27ae60;
        }
        .exp-step-1 {
            top: 20px;
            left: 10px;
            background-color: #f4ecf7;
            border: 1px solid #8e44ad;
        }
        .exp-step-2 {
            top: 80px;
            left: 50%;
            transform: translateX(-50%);
            background-color: #f4ecf7;
            border: 1px solid #8e44ad;
        }
        .exp-step-3 {
            bottom: 20px;
            right: 10px;
            background-color: #f4ecf7;
            border: 1px solid #8e44ad;
        }
        .step-icon {
            margin-right: 8px;
            font-size: 16px;
        }
        .sit-step .step-icon {
            color: #27ae60;
        }
        .exp-step .step-icon {
            color: #8e44ad;
        }
        .path-arrow {
            position: absolute;
            z-index: 1;
        }
        .arrow-1 {
            width: 100px;
            height: 2px;
            background-color: #27ae60;
            top: 50px;
            left: 70px;
            transform: rotate(30deg);
        }
        .arrow-1::after {
            content: "";
            position: absolute;
            right: -5px;
            top: -4px;
            width: 0;
            height: 0;
            border-left: 10px solid #27ae60;
            border-top: 5px solid transparent;
            border-bottom: 5px solid transparent;
        }
        .arrow-2 {
            width: 100px;
            height: 2px;
            background-color: #27ae60;
            bottom: 50px;
            right: 70px;
            transform: rotate(-30deg);
        }
        .arrow-2::after {
            content: "";
            position: absolute;
            right: -5px;
            top: -4px;
            width: 0;
            height: 0;
            border-left: 10px solid #27ae60;
            border-top: 5px solid transparent;
            border-bottom: 5px solid transparent;
        }
        .arrow-3 {
            width: 90px;
            height: 2px;
            background-color: #8e44ad;
            top: 50px;
            left: 70px;
            transform: rotate(30deg);
        }
        .arrow-3::after {
            content: "";
            position: absolute;
            right: -5px;
            top: -4px;
            width: 0;
            height: 0;
            border-left: 10px solid #8e44ad;
            border-top: 5px solid transparent;
            border-bottom: 5px solid transparent;
        }
        .arrow-4 {
            width: 90px;
            height: 2px;
            background-color: #8e44ad;
            bottom: 50px;
            right: 70px;
            transform: rotate(-30deg);
        }
        .arrow-4::after {
            content: "";
            position: absolute;
            right: -5px;
            top: -4px;
            width: 0;
            height: 0;
            border-left: 10px solid #8e44ad;
            border-top: 5px solid transparent;
            border-bottom: 5px solid transparent;
        }
        .path-features {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
        }
        .feature-item {
            display: flex;
            align-items: flex-start;
            margin-bottom: 10px;
        }
        .feature-item:last-child {
            margin-bottom: 0;
        }
        .feature-icon {
            margin-right: 10px;
            flex-shrink: 0;
            color: #27ae60;
            font-size: 16px;
        }
        .exploratory .feature-icon {
            color: #8e44ad;
        }
        .feature-text {
            font-size: 14px;
            line-height: 1.5;
        }
        .current-project {
            position: absolute;
            top: 10px;
            right: 10px;
            background-color: #f39c12;
            color: white;
            padding: 5px 12px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: bold;
            transform: rotate(5deg);
            box-shadow: 0 2px 5px rgba(0,0,0,0.2);
        }
        .case-example {
            margin-top: 20px;
            background-color: #f0f9ff;
            border-left: 3px solid #3498db;
            padding: 15px;
            border-radius: 0 8px 8px 0;
        }
        .case-title {
            font-weight: bold;
            margin-bottom: 10px;
            color: #2c3e50;
            display: flex;
            align-items: center;
        }
        .case-icon {
            margin-right: 10px;
            color: #3498db;
        }
        .case-text {
            font-size: 14px;
            line-height: 1.5;
            color: #34495e;
        }
        .download-btn {
            background-color: #3498db;
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            transition: all 0.3s;
            display: flex;
            align-items: center;
            margin: 30px auto 0;
            box-shadow: 0 3px 10px rgba(52,152,219,0.3);
        }
        .download-btn:hover {
            background-color: #2980b9;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(52,152,219,0.4);
        }
        .download-btn svg {
            margin-right: 10px;
        }
        @media (max-width: 768px) {
            .paths-comparison {
                flex-direction: column;
            }
            .path {
                margin-bottom: 20px;
            }
        }
    </style>
    <!-- 使用Font Awesome图标 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
</head>
<body>
    <div class="comparison-container" id="comparisonContainer">
        <h2>人工智能教育的两种路径</h2>
 
        
        <div class="tag-container">
            
        </div>
        
        <div class="paths-comparison">
            <!-- 情境创设-问题解决型 -->
            <div class="path">
                <div class="path-header situational-header">
                    情境创设-问题解决型
                </div>
                <div class="current-project">本案例采用</div>
                <div class="path-content">
                    <div class="path-diagram">
                        <div class="path-step sit-step sit-step-1">
                            <i class="step-icon fas fa-question-circle"></i>
                            <span>真实问题</span>
                        </div>
                        <div class="path-arrow arrow-1"></div>
                        
                        <div class="path-step sit-step sit-step-2">
                            <i class="step-icon fas fa-lightbulb"></i>
                            <span>技术选择</span>
                        </div>
                        <div class="path-arrow arrow-2"></div>
                        
                        <div class="path-step sit-step sit-step-3">
                            <i class="step-icon fas fa-check-circle"></i>
                            <span>解决方案</span>
                        </div>
                    </div>
                    
                    <div class="path-features">
                        <div class="feature-item">
                            <div class="feature-icon"><i class="fas fa-thumbs-up"></i></div>
                            <div class="feature-text">从生活实际问题出发，提高学习动机和参与度</div>
                        </div>
                        <div class="feature-item">
                            <div class="feature-icon"><i class="fas fa-thumbs-up"></i></div>
                            <div class="feature-text">技术应用于真实场景，增强学习意义感</div>
                        </div>
                        <div class="feature-item">
                            <div class="feature-icon"><i class="fas fa-thumbs-up"></i></div>
                            <div class="feature-text">培养解决问题的实用思维与创新能力</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 对比探究-原理理解型 -->
            <div class="path">
                <div class="path-header exploratory-header">
                    对比探究-原理理解型
                </div>
                <div class="path-content">
                    <div class="path-diagram">
                        <div class="path-step exp-step exp-step-1">
                            <i class="step-icon fas fa-microscope"></i>
                            <span>对照实验</span>
                        </div>
                        <div class="path-arrow arrow-3"></div>
                        
                        <div class="path-step exp-step exp-step-2">
                            <i class="step-icon fas fa-sync-alt"></i>
                            <span>比较分析</span>
                        </div>
                        <div class="path-arrow arrow-4"></div>
                        
                        <div class="path-step exp-step exp-step-3">
                            <i class="step-icon fas fa-brain"></i>
                            <span>原理理解</span>
                        </div>
                    </div>
                    
                    <div class="path-features exploratory">
                        <div class="feature-item">
                            <div class="feature-icon"><i class="fas fa-thumbs-up"></i></div>
                            <div class="feature-text">通过对比实验深入理解算法原理</div>
                        </div>
                        <div class="feature-item">
                            <div class="feature-icon"><i class="fas fa-thumbs-up"></i></div>
                            <div class="feature-text">培养科学探究精神和逻辑思维能力</div>
                        </div>
                        <div class="feature-item">
                            <div class="feature-icon"><i class="fas fa-thumbs-up"></i></div>
                            <div class="feature-text">适合需要深度理解技术原理的学习目标</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="case-example">
            <div class="case-title">
                <i class="case-icon fas fa-quote-left"></i>
                智能宠物投喂器案例
            </div>
            <div class="case-text">
                "宠物独自在家怎么办"这一真实问题为起点，引导学生用人工智能技术搭建解决方案，体现情境创设-问题解决型路径的优势。教学实践表明，根据学生特点、教学内容和目标灵活选择合适的教学路径至关重要。
            </div>
        </div>
    </div>

    <button class="download-btn" id="downloadBtn">
        <svg width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
            <polyline points="7 10 12 15 17 10"></polyline>
            <line x1="12" y1="15" x2="12" y2="3"></line>
        </svg>
        下载教学路径图
    </button>

    <script src="https://html2canvas.hertzen.com/dist/html2canvas.min.js"></script>
    <script>
        document.getElementById('downloadBtn').addEventListener('click', function() {
            const element = document.getElementById('comparisonContainer');
            const originalWidth = element.offsetWidth;
            const originalHeight = element.offsetHeight;
            const scale = 2;
            
            html2canvas(element, {
                backgroundColor: "#ffffff",
                scale: scale,
                width: originalWidth,
                height: originalHeight,
                windowWidth: originalWidth * scale,
                windowHeight: originalHeight * scale
            }).then(canvas => {
                const link = document.createElement('a');
                link.download = '人工智能教育两种路径对比.png';
                link.href = canvas.toDataURL('image/png');
                link.click();
            });
        });
    </script>
</body>
</html>