<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>初先生 - 公众号底部名片</title>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@400;500;600;700&display=swap" rel="stylesheet">
    <style>
        @font-face {
            font-family: 'Alibaba PuHuiTi';
            src: url('https://cdn.jsdelivr.net/gh/alibaba/fonts@main/puhuiti/AlibabaPuHuiTi-3-55-Regular.woff2') format('woff2');
            font-weight: 400;
            font-style: normal;
            font-display: swap;
        }

        @font-face {
            font-family: 'Alibaba PuHuiTi';
            src: url('https://cdn.jsdelivr.net/gh/alibaba/fonts@main/puhuiti/AlibabaPuHuiTi-3-65-Medium.woff2') format('woff2');
            font-weight: 500;
            font-style: normal;
            font-display: swap;
        }
    </style>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        .business-card {
            width: 1920px;
            height: 1080px;
            margin: 20px auto;
            background: linear-gradient(135deg, #E6F5EE 0%, #E6F5EE 50%, #FFD966 100%);
            border-radius: 48px;
            padding: 80px 100px;
            font-family: 'Alibaba PuHuiTi', 'Noto Sans SC', 'PingFang SC', 'Microsoft YaHei', sans-serif;
            box-shadow: 0 20px 50px rgba(0, 0, 0, 0.12);
            /* 超高清 1920x1080 优化 */
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            image-rendering: -webkit-optimize-contrast;
            image-rendering: crisp-edges;
            transform: scale(1);
            transform-origin: center;
            position: relative;
        }

        .card-content {
            display: flex;
            align-items: center;
            gap: 150px;
            justify-content: space-between;
            width: 100%;
            height: 100%;
        }

        .left-section {
            flex: 1;
            text-align: center;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            max-width: 600px;
        }

        .avatar {
            width: 480px;
            height: 480px;
            border-radius: 50%;
            background-image: url('https://img.ctnas.com/ctnas-img/2025/07/39fd67b2f2a6683dc21dbd839495b8cc.20250702122340450.webp');
            background-size: cover;
            background-position: center;
            margin: 20px auto 50px;
            box-shadow: 0 15px 45px rgba(7, 148, 82, 0.4);
            border: 10px solid #fff;
            /* 超高清图片优化 */
            image-rendering: -webkit-optimize-contrast;
            image-rendering: crisp-edges;
            transform: scale(1);
            backface-visibility: hidden;
        }

        .name {
            font-size: 125px;
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 35px;
            letter-spacing: 2px;
            /* 超高清文字优化 */
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            text-shadow: 0 4px 8px rgba(0, 0, 0, 0.08);
        }

        .title {
            font-size: 48px;
            color: #079452;
            font-weight: 600;
            margin-bottom: 20px;
            letter-spacing: 3px;
            /* 超高清文字优化 */
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }

        .subtitle {
            font-size: 14px;
            color: #64748b;
            line-height: 1.5;
        }

        .right-section {
            flex: 1;
            text-align: center;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            max-width: 600px;
        }

        .qr-code {
            width: 450px;
            height: 450px;
            background-image: url('https://img.ctnas.com/ctnas-img/2025/07/90613de67065fdcf5efb6e5aeab8b955.20250702122347081.webp');
            background-size: cover;
            background-position: center;
            border-radius: 30px;
            margin: 0 auto 40px;
            box-shadow: 0 12px 30px rgba(0, 0, 0, 0.15);
            border: 8px solid #E6F5EE;
            /* 超高清图片优化 */
            image-rendering: -webkit-optimize-contrast;
            image-rendering: crisp-edges;
        }

        .qr-text {
            font-size: 38px;
            color: #1e293b;
            font-weight: 500;
            line-height: 1.5;
            letter-spacing: 1px;
            /* 超高清文字优化 */
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .business-card {
                padding: 30px 20px;
                margin: 10px;
            }

            .card-content {
                flex-direction: column;
                gap: 30px;
            }

            .avatar {
                width: 100px;
                height: 100px;
                font-size: 40px;
            }

            .name {
                font-size: 28px;
            }

            .qr-code {
                width: 100px;
                height: 100px;
            }
        }
    </style>
</head>
<body>
    <div class="business-card">
        <div class="card-content">
            <!-- 左侧个人信息 -->
            <div class="left-section">
                <div class="avatar"></div>
                <div class="name">初先生</div>
                <div class="title">懂技术 | 精业务 | 善落地</div>

            </div>

            <!-- 右侧二维码 -->
            <div class="right-section">
                <div class="qr-code"></div>
                <div class="qr-text">
                    欢迎链接我，一起探讨更多<br>
                    备注「公众号」
                </div>
            </div>
        </div>
    </div>
</body>
</html>