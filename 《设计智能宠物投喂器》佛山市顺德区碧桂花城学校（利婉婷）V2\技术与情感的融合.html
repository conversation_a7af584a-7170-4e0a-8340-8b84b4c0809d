<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>技术与情感的融合</title>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            display: flex;
            flex-direction: column;
            align-items: center;
            background-color: #f5f7fa;
            padding: 20px;
            color: #333;
        }
        .container {
            max-width: 900px;
            margin-bottom: 30px;
        }
        .fusion-card {
            background-color: white;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            padding: 30px;
            margin-bottom: 20px;
            position: relative;
            overflow: hidden;
        }
        .title {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 30px;
            font-size: 24px;
            font-weight: bold;
            position: relative;
            z-index: 2;
        }
        .gradient-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 8px;
            background: linear-gradient(90deg, #3498db, #e74c3c);
        }
        .fusion-visual {
            display: flex;
            justify-content: center;
            margin: 30px 0;
            position: relative;
        }
        .tech-circle, .emotion-circle {
            width: 180px;
            height: 180px;
            border-radius: 50%;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            color: white;
            position: relative;
            z-index: 1;
        }
        .tech-circle {
            background-color: #3498db;
            margin-right: -40px;
        }
        .emotion-circle {
            background-color: #e74c3c;
            margin-left: -40px;
        }
        .intersection {
            width: 100px;
            height: 100px;
            border-radius: 50%;
            background-color: #9b59b6;
            position: relative;
            z-index: 2;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            box-shadow: 0 0 15px rgba(155,89,182,0.5);
        }
        .circle-icon {
            font-size: 40px;
            margin-bottom: 10px;
        }
        .circle-text {
            font-size: 14px;
            text-align: center;
            padding: 0 15px;
        }
        .quote-box {
            background-color: #f8f9fa;
            border-left: 4px solid #9b59b6;
            padding: 20px;
            margin: 30px 0;
            position: relative;
            z-index: 2;
        }
        .quote-text {
            font-style: italic;
            color: #555;
            margin-bottom: 10px;
            font-size: 16px;
        }
        .quote-attribution {
            text-align: right;
            color: #888;
            font-size: 14px;
        }
        .value-statement {
            background-color: #2c3e50;
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin: 30px 0;
            position: relative;
            z-index: 2;
        }
        .transformation {
            display: flex;
            justify-content: space-between;
            margin: 40px 0;
            position: relative;
        }
        .transformation:after {
            content: "";
            position: absolute;
            top: 50%;
            left: 10%;
            right: 10%;
            height: 3px;
            background: linear-gradient(90deg, #3498db, #e74c3c);
            z-index: 0;
        }
        .transformation-step {
            width: 110px;
            background-color: white;
            border-radius: 10px;
            padding: 15px;
            box-shadow: 0 3px 10px rgba(0,0,0,0.1);
            text-align: center;
            position: relative;
            z-index: 1;
        }
        .step-icon {
            font-size: 30px;
            margin-bottom: 10px;
        }
        .step-text {
            font-size: 14px;
            color: #555;
        }
        .download-btn {
            background-color: #9b59b6;
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            transition: all 0.3s;
            display: flex;
            align-items: center;
            margin: 30px auto 0;
            box-shadow: 0 3px 10px rgba(155,89,182,0.3);
        }
        .download-btn:hover {
            background-color: #8e44ad;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(155,89,182,0.4);
        }
        .download-btn svg {
            margin-right: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="fusion-card" id="infographic">
            <div class="gradient-overlay"></div>
            <div class="title">技术与情感的融合</div>
            
            <div class="fusion-visual">
                <div class="tech-circle">
                    <div class="circle-icon">💻</div>
                    <div class="circle-text">技术</div>
                </div>
                <div class="intersection">
                    <div class="circle-text">融合创新</div>
                </div>
                <div class="emotion-circle">
                    <div class="circle-icon">❤️</div>
                    <div class="circle-text">情感</div>
                </div>
            </div>
            
            <div class="quote-box">
                <div class="quote-text">"能不能也识别宠物是寂寞还是饥饿？"</div>
                <div class="quote-attribution">— 四年级学生提问</div>
            </div>
            
            <div class="value-statement">
                人工智能教育的重要价值——技术不只是冰冷的代码，而是转化情感为解决方案的工具，让孩子们从爱护生命的视角理解编程的温度与意义。
            </div>
            
            <div class="transformation">
                <div class="transformation-step">
                    <div class="step-icon">📊</div>
                    <div class="step-text">冰冷代码</div>
                </div>
                <div class="transformation-step">
                    <div class="step-icon">🔄</div>
                    <div class="step-text">技术学习</div>
                </div>
                <div class="transformation-step">
                    <div class="step-icon">💡</div>
                    <div class="step-text">情感融入</div>
                </div>
                <div class="transformation-step">
                    <div class="step-icon">🐾</div>
                    <div class="step-text">关爱生命</div>
                </div>
                <div class="transformation-step">
                    <div class="step-icon">✨</div>
                    <div class="step-text">有温度的技术</div>
                </div>
            </div>
        </div>
    </div>

    <button class="download-btn" id="downloadBtn">
        <svg width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
            <polyline points="7 10 12 15 17 10"></polyline>
            <line x1="12" y1="15" x2="12" y2="3"></line>
        </svg>
        下载"技术与情感融合"图表
    </button>

    <script src="https://html2canvas.hertzen.com/dist/html2canvas.min.js"></script>
    <script>
        document.getElementById('downloadBtn').addEventListener('click', function() {
            const element = document.getElementById('infographic');
            const originalWidth = element.offsetWidth;
            const originalHeight = element.offsetHeight;
            const scale = 2;
            
            html2canvas(element, {
                backgroundColor: null,
                scale: scale,
                width: originalWidth,
                height: originalHeight,
                windowWidth: originalWidth * scale,
                windowHeight: originalHeight * scale
            }).then(canvas => {
                const link = document.createElement('a');
                link.download = '技术与情感的融合.png';
                link.href = canvas.toDataURL('image/png');
                link.click();
            });
        });
    </script>
</body>
</html>