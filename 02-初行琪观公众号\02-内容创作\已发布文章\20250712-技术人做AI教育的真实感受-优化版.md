# 重构人工智能教学平台：一个技术人的踩坑实录


大家好，我是初先生，一个懂技术，精业务，善落地的80后复合型奶爸。

由于现在我们的人工智能教学平台不堪重负，所以需要重构。

从6月底到现在，每天都加班到凌晨。我本来给自己定了个规矩：不能超过晚上11点。

结果呢？昨天晚上11点还在调试图片存储的bug，我太太来问："又加班？"我说："快了快了..."然后一直折腾到凌晨1点。

今天看到一篇文章说"10%的能力价值涨了1000倍"，我当时就想：这不就是在说我吗？

今天想和大家分享一下我做这个人工智能教育项目的真实感受，不是成功经验，是踩坑实录。

---

## 先说说我现在的"摊子"


目前我在重构人工智能教育教学平台，差不多1000多个用户，主要是给小学生和老师使用。

听起来挺简单的，对吧？我当初也是这么想的。

就拿今天来说，我在朋友圈发了个状态：

> #教学平台重构  
> 重构比我预想中难：  
> 1、学生的前端修改  
> 2、图片对话存储的数据库  
> 3、教室端个性化教学&心理健康预防管理  
> 4、管理员端安全维护检测设置等  
> 
> 每天花固定4小时开发，一天8+4工作时间

看到了吗？这就是我现在的日常。每天4小时开发，听起来不多，但这4小时...怎么说呢，密度挺高的。

---

## 踩过的几个坑

### 第一个坑：数据库选择"

我最开始想得很简单：用SQLite做数据库，轻量级，够用了。

开发到高级功能，发现SQLite**并发处理能力不足**，结合应用需求最后不得不迁移到PostgreSQL。

PostgreSQL，支持复杂查询和高并发，但迁移过程...那叫一个折腾。

### 第二个坑：AI对话模型选择的纠结

平台里有个"小画姐姐"AI助手，学生可以和她聊天。听起来简单，但选哪个模型就让我纠结了好久。

**成本vs效果的平衡**：
- 付费的太贵，1000+个学生用下来一个月得花不少钱
- 便宜的效果一般，对话质量参差不齐

**安全限制更头疼**：
- 学生什么都敢问，涉及到未成年人保护
- 是在提示词层面限制，还是加个中间件过滤？
- 过滤太严，回答速度就慢；过滤太松，又担心安全问题

最后我选择了中间件方案，在请求前就过滤掉不合适的内容。同时为了控制成本，没有在提示词里加太多限制条件——token越多，响应时间越长，费用也越高。

这套方案的测试和调试...让我连续好几个凌晨都在加班。



### 第三个坑：心理健康监测这个"意外功能"

既然我们有孩子的对话记录和绘画作品，我就想：能不能基于这些数据，增加个监测学生情绪的功能？现在有些孩子压力大，但不愿意主动说出来。

我当时想：这应该不难吧，就是个数据分析问题。

结果一研究才发现，这哪里是技术问题，这是教育心理学问题啊！

要做科学的情绪分析，得了解皮亚杰认知发展理论、维果茨基社会文化理论、埃里克森心理社会发展理论...

我一个搞技术的，突然要去啃心理学教材，那感觉...就像让一个厨师去做心理咨询一样。

最后发现，要做一个真正科学的教育发展评估系统，比重构整个平台还要复杂。

---

## 意外收获

虽然踩了不少坑，但也有一些意外收获：

### 1. 发现了"懂技术，精业务"的价值

以前我只关注技术实现，现在开始思考：
- 这个功能对学生真的有用吗？
- 老师会怎么使用这个工具？
- 使用的价值在哪里？

这种**技术+教育**的复合思维，让我看问题的角度完全不同了。


### 2. 体会到了不一样的责任感

做教育和做其他项目不一样，你面对的是孩子，是未来。

每次看到学生用我们的平台学到新知识，那种成就感...怎么说呢，比解决一个复杂bug还要爽。

---

## 几个真实感受

### 关于时间管理

每天4小时开发，加上其他工作，一天要工作12小时。

说实话，有时候挺累的。特别是当奶爸，还要陪孩子，时间真的不够用。

但我发现，**有限的时间反而让我更专注**。以前可能会纠结技术细节，现在更关注用户价值。

### 关于教育行业

**教育真的是个慢行业**。

不像互联网项目，可以快速迭代，快速试错。教育需要更多的耐心和责任心。毕竟，你的每个决策都可能影响孩子的学习体验。

### 关于AI教育

**AI是工具，不是目的**。

我们不是为了用AI而用AI，而是要思考：AI怎么能真正帮助教育变得更好？

---

## 给同样在转型的朋友几个建议

### 别太把自己当回事

技术背景确实是优势，但别以为懂技术就懂教育。

我现在经常和老师、学生聊天，问他们真正需要什么。有时候他们的一句话，比我优化半天还管用。

### 做好打持久战的准备

转型这事儿吧，急不来。

我现在每天都在学新东西，有时候觉得自己像个小学生。但没办法，这就是现实。

### 想清楚为啥要做这事

累的时候，我就想想那些用我们平台学习的孩子。

对我来说，是希望AI能真正帮助到教育，帮助到孩子们。这个初心，支撑着我继续折腾下去。

---

## 最后多聊几句

我这人吧，专家算不上，成功案例也谈不上，就是个正在摸索的技术人。

分享这些踩坑经历，主要是希望给同样在折腾的朋友一些参考。说不定我踩过的坑，能让你们少走点弯路。

反正就一句话：咱们一起学习，一起避坑，一起加油。



