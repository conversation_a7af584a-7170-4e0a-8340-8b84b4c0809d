<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>结构化知识脉络</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background-color: #ffffff;
            color: #333;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            display: flex;
            flex-direction: column;
            align-items: center;
        }
        .knowledge-container {
            background-color: white;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            padding: 30px;
            margin-bottom: 20px;
            width: 100%;
            max-width: 800px;
        }
        h2 {
            text-align: center;
            color: #2c3e50;
            margin-bottom: 10px;
            font-size: 24px;
        }
        .quote {
            text-align: center;
            color: #7f8c8d;
            font-style: italic;
            margin-bottom: 25px;
            padding: 10px 30px;
            background-color: #f8f9fa;
            border-radius: 8px;
            font-size: 15px;
            position: relative;
        }
        .quote::before, .quote::after {
            content: '"';
            font-size: 30px;
            color: #bdc3c7;
            position: absolute;
            top: 5px;
        }
        .quote::before {
            left: 10px;
        }
        .quote::after {
            right: 10px;
        }
        .building-blocks {
            display: grid;
            grid-template-columns: 1fr;
            gap: 25px;
            margin-bottom: 30px;
        }
        .block {
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        }
        .block-header {
            padding: 15px;
            color: white;
            font-weight: bold;
            font-size: 18px;
            display: flex;
            align-items: center;
        }
        .pipeline-header {
            background-color: #3498db;
        }
        .algorithm-header {
            background-color: #9b59b6;
        }
        .value-header {
            background-color: #e74c3c;
        }
        .block-icon {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background-color: rgba(255,255,255,0.3);
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 12px;
            font-size: 18px;
        }
        .block-content {
            padding: 20px;
            background-color: #fff;
        }
        .flow-steps {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 15px;
        }
        .step-box {
            width: 30%;
            padding: 12px;
            border-radius: 8px;
            text-align: center;
            font-weight: bold;
            font-size: 16px;
        }
        .pipeline-step {
            background-color: #ebf5fb;
            border: 1px solid #3498db;
            color: #2980b9;
        }
        .algorithm-step {
            background-color: #f4ecf7;
            border: 1px solid #9b59b6;
            color: #8e44ad;
        }
        .value-step {
            background-color: #fdedec;
            border: 1px solid #e74c3c;
            color: #c0392b;
        }
        .step-detail {
            font-size: 13px;
            color: #7f8c8d;
            font-weight: normal;
            margin-top: 5px;
        }
        .flow-arrow {
            width: 40px;
            height: 4px;
            background: currentColor;
            position: relative;
        }
        .pipeline-arrow {
            color: #3498db;
        }
        .algorithm-arrow {
            color: #9b59b6;
        }
        .value-arrow {
            color: #e74c3c;
        }
        .flow-arrow::after {
            content: "";
            position: absolute;
            right: 0;
            top: -4px;
            width: 0;
            height: 0;
            border-left: 8px solid currentColor;
            border-top: 6px solid transparent;
            border-bottom: 6px solid transparent;
        }
        .metaphor {
            font-style: italic;
            color: #34495e;
            background-color: #f8f9fa;
            padding: 10px 15px;
            border-radius: 8px;
            margin-top: 15px;
            display: flex;
            align-items: center;
        }
        .metaphor-icon {
            font-size: 20px;
            margin-right: 10px;
            color: #7f8c8d;
        }
        .download-btn {
            background-color: #3498db;
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            transition: all 0.3s;
            display: flex;
            align-items: center;
            margin: 30px auto 0;
            box-shadow: 0 3px 10px rgba(52,152,219,0.3);
        }
        .download-btn:hover {
            background-color: #2980b9;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(52,152,219,0.4);
        }
        .download-btn svg {
            margin-right: 10px;
        }
        @media (max-width: 768px) {
            .flow-steps {
                flex-direction: column;
                gap: 15px;
            }
            .step-box {
                width: 80%;
            }
            .flow-arrow {
                transform: rotate(90deg);
                margin: 10px 0;
            }
        }
    </style>
    <!-- 使用Font Awesome图标 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
</head>
<body>
    <div class="knowledge-container" id="knowledgeContainer">
        <h2></h2>
      
        
        <div class="building-blocks">
            <!-- 数据流水线 -->
            <div class="block">
                <div class="block-header pipeline-header">
                    <div class="block-icon"><i class="fas fa-water"></i></div>
                    数据流水线
                </div>
                <div class="block-content">
                    <div class="flow-steps">
                        <div class="step-box pipeline-step">
                            听见
                            <div class="step-detail">（采集）</div>
                        </div>
                        <div class="flow-arrow pipeline-arrow"></div>
                        
                        <div class="step-box pipeline-step">
                            听清
                            <div class="step-detail">（清洗）</div>
                        </div>
                        <div class="flow-arrow pipeline-arrow"></div>
                        
                        <div class="step-box pipeline-step">
                            听懂
                            <div class="step-detail">（标注）</div>
                        </div>
                    </div>
                    
                    <div class="metaphor">
                        <i class="metaphor-icon fas fa-quote-left"></i>
                        <div>就像教AI理解宠物语言的三部曲</div>
                    </div>
                </div>
            </div>
            
            <!-- 算法黑匣子 -->
            <div class="block">
                <div class="block-header algorithm-header">
                    <div class="block-icon"><i class="fas fa-box"></i></div>
                    算法黑匣子
                </div>
                <div class="block-content">
                    <div class="flow-steps">
                        <div class="step-box algorithm-step">
                            输入
                            <div class="step-detail">（汪汪叫）</div>
                        </div>
                        <div class="flow-arrow algorithm-arrow"></div>
                        
                        <div class="step-box algorithm-step">
                            处理
                            <div class="step-detail">（拆解声纹）</div>
                        </div>
                        <div class="flow-arrow algorithm-arrow"></div>
                        
                        <div class="step-box algorithm-step">
                            输出
                            <div class="step-detail">（放粮指令）</div>
                        </div>
                    </div>
                    
                    <div class="metaphor">
                        <i class="metaphor-icon fas fa-quote-left"></i>
                        <div>孩子们后来称之为"宠语翻译机"</div>
                    </div>
                </div>
            </div>
            
            <!-- 价值转换器 -->
            <div class="block">
                <div class="block-header value-header">
                    <div class="block-icon"><i class="fas fa-exchange-alt"></i></div>
                    价值转换器
                </div>
                <div class="block-content">
                    <div class="flow-steps">
                        <div class="step-box value-step">
                            技术能力
                            <div class="step-detail">（AI模型）</div>
                        </div>
                        <div class="flow-arrow value-arrow"></div>
                        
                        <div class="step-box value-step">
                            应用场景
                            <div class="step-detail">（宠物喂食）</div>
                        </div>
                        <div class="flow-arrow value-arrow"></div>
                        
                        <div class="step-box value-step">
                            解决问题
                            <div class="step-detail">（自动投喂）</div>
                        </div>
                    </div>
                    
                    <div class="metaphor">
                        <i class="metaphor-icon fas fa-quote-left"></i>
                        <div>这就像搭建一座桥，连接AI技术与现实生活</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <button class="download-btn" id="downloadBtn">
        <svg width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
            <polyline points="7 10 12 15 17 10"></polyline>
            <line x1="12" y1="15" x2="12" y2="3"></line>
        </svg>
        下载知识脉络图
    </button>

    <script src="https://html2canvas.hertzen.com/dist/html2canvas.min.js"></script>
    <script>
        document.getElementById('downloadBtn').addEventListener('click', function() {
            const element = document.getElementById('knowledgeContainer');
            const originalWidth = element.offsetWidth;
            const originalHeight = element.offsetHeight;
            const scale = 2;
            
            html2canvas(element, {
                backgroundColor: "#ffffff",
                scale: scale,
                width: originalWidth,
                height: originalHeight,
                windowWidth: originalWidth * scale,
                windowHeight: originalHeight * scale
            }).then(canvas => {
                const link = document.createElement('a');
                link.download = '结构化知识脉络.png';
                link.href = canvas.toDataURL('image/png');
                link.click();
            });
        });
    </script>
</body>
</html>