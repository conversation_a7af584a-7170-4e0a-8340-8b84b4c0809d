/**
 * Miss 利 - 专业科技教育主题 (20250611版)
 * 主题色: #1E90FF (深天蓝色)
 * 特点: 根据新的排版样式优化一、二、三级标题。
 */

/* 顶层容器样式 */
container {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', 'Microsoft YaHei', sans-serif;
  line-height: 1.75;
  color: #333333;
  padding: 10px 5px;
}

/* 一级标题样式 (20250611优化版 v2) */
h1 {
  color: #333333; /* 深色文字 */
  font-size: 1.6em;
  font-weight: 600;
  margin-top: 1.8em;
  margin-bottom: 1em;
  background-color: transparent; /* 透明背景 */
  padding: 0.5em 0; /* 调整上下内边距，移除左右内边距 */
  border-radius: 0; /* 移除圆角 */
  text-align: left; /* 文字左对齐 */
  border-left: none; /* 移除左边框 */
  border-bottom: 3px solid #1E90FF; /* 主题色下划线 */
  padding-bottom: 0.4em; /* 下划线与文字间距 */
}

/* 二级标题样式 (20250611优化版 v5 - 左侧竖线) */
h2 {
  color: #333333; /* 深灰色文字 */
  font-size: 1.4em;
  font-weight: 600;
  margin-top: 1.6em;
  margin-bottom: 0.8em;
  background-color: transparent; /* 透明背景 */
  padding: 0.3em 0 0.3em 0.6em; /* 上下内边距，左侧内边距 */
  border-radius: 0; /* 移除圆角 */
  display: block; /* 恢复为块级元素 */
  border: none; /* 移除所有旧边框 */
  border-left: 3px solid #1E90FF; /* 主题色左竖线 */
  text-align: left; /* 文字左对齐 */
}

/* 三级标题样式 (20250611优化版) */
h3 {
  color: #1E90FF; /* 主题色文字 */
  font-size: 1.2em;
  font-weight: 600;
  margin-top: 1.4em;
  margin-bottom: 0.6em;
  border-left: 3px solid #1E90FF; /* 主题色左竖线 */
  padding-left: 0.5em; /* 左竖线与文字间距 */
  border-bottom: 1px dotted #1E90FF; /* 主题色虚线 */
  padding-bottom: 0.2em; /* 下划线与文字间距 */
}

/* 段落样式 */
p {
  margin-bottom: 1.2em;
  letter-spacing: 0.5px;
}

/* 图片样式 */
image, img {
  border-radius: 6px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-top: 1em;
  margin-bottom: 1em;
  display: block;
  max-width: 100%;
}

/* 引用样式 */
blockquote {
  border-left: 4px solid #1E90FF;
  background-color: rgba(30, 144, 255, 0.05);
  color: #555555;
  padding: 12px 18px;
  margin: 1.5em 0;
  border-radius: 0 4px 4px 0;
}

blockquote_p {
  margin-bottom: 0.5em;
}

/* 金句专属样式 */
.golden-quote {
  border: none;
  background-color: transparent;
  text-align: center;
  padding: 2em 0;
  margin: 2em 0;
}
.golden-quote p {
  font-size: 1.2em;
  font-weight: 600;
  color: #1E90FF;
  letter-spacing: 1px;
}
.golden-quote::before {
  content: '“';
  font-size: 3em;
  color: rgba(30, 144, 255, 0.2);
  margin-right: 0.2em;
  font-family: serif;
}

/* 分割线样式 */
hr {
  border: 0;
  height: 1px;
  background-image: linear-gradient(to right, rgba(30, 144, 255, 0), rgba(30, 144, 255, 0.75), rgba(30, 144, 255, 0));
  margin: 2em 0;
}

/* 行内代码样式 */
codespan, p > code, li > code {
  background-color: rgba(30, 144, 255, 0.1);
  color: #c7254e;
  padding: .2em .4em;
  margin: 0 2px;
  border-radius: 3px;
  font-size: 0.9em;
  font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, Courier, monospace;
}

/* 粗体样式 */
strong {
  font-weight: 600;
  color: #1E90FF; /* 所有加粗文字统一使用主题色 */
}

/* 链接样式 */
link, wx_link, a {
  color: #1E90FF;
  text-decoration: none;
  border-bottom: 1px dotted #1E90FF;
  padding-bottom: 1px;
}
link:hover, wx_link:hover, a:hover {
  color: #007acc;
  border-bottom: 1px solid #007acc;
}

/* 列表样式 */
ul, ol {
  padding-left: 1.8em;
  margin-bottom: 1em;
}
listitem, li {
  margin-bottom: 0.5em;
}

/* 代码块样式 */
code_pre {
  background-color: #f8f9fa;
  border: 1px solid #dee2e6;
  padding: 1em;
  margin: 1.5em 0;
  border-radius: 6px;
  overflow-x: auto;
  font-size: 0.95em;
}
code_pre code {
  font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, Courier, monospace;
  color: #333333;
  background-color: transparent;
  padding: 0;
  margin: 0;
}

/* GFM 提示框样式 (统一品牌色最终版) */
blockquote_note,
blockquote_tip,
blockquote_warning,
blockquote_important,
blockquote_caution {
  background-color: rgba(30, 144, 255, 0.05); /* 统一使用主题色淡背景 */
  border-left: 5px solid #1E90FF;       /* 统一使用主题色左边框 */
  padding: 12px 20px;
  margin: 1.5em 0;
  border-radius: 4px;
}

blockquote_title {
  font-weight: 600;
  margin-bottom: 0.5em;
}

blockquote_title_note,
blockquote_title_tip,
blockquote_title_warning,
blockquote_title_important,
blockquote_title_caution {
  color: #1E90FF; /* 所有标题统一使用主题色 */
}