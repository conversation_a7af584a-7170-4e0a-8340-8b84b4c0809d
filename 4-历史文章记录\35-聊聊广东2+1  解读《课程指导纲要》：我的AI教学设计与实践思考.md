# 聊聊广东"2+1" | 解读《课程指导纲要》：我的AI教学设计与实践思考

前两篇我们聊了广东"2+1"方案中的教师素养框架和学生素养框架，今天我们来聊聊第三个重要文件——《广东省中小学人工智能课程指导纲要》。

当我第一次拿到这份纲要时，老实说，内心是有点忐忑的。

毕竟从文件到课堂，从理论到实践，中间还有很长的路要走。作为一线小学信息科技教师，我总在想：这些宏观的课程目标和内容，怎样才能转化为孩子们喜欢、能理解、有收获的课堂活动呢？

带着这个问题，我反复研读了纲要，又回顾了这两年我的教学实践，发现不少我摸索出来的方法竟然和纲要的理念不谋而合！今天就和大家分享一下，我是如何在小学阶段落实这份纲要的精神，以及我的一些真实案例和思考。

## 一、纲要中的小学阶段：感知与体验的智能启蒙

纲要把小学阶段定位为"感知与体验"，强调让孩子们通过直观、生动的方式感受AI技术，而不是上来就讲复杂的理论和概念。

回想我最早带孩子们接触AI时，就是从最直观的体验入手的。记得那时我录制了一些AI绘画的视频在课上播放，结果虽然孩子们惊叹连连，但总觉得和他们"隔着一层纱"。

后来我意识到，小学生需要的是真正的"沉浸式"体验。2024年暑假，我开始尝试在"AI创新探索营"中带孩子们体验AI绘画。

第一期营我们用的是豆包，界面简洁，孩子们操作起来还算顺手。可是到了第二期，随着豆包的升级迭代，界面突然多了很多功能模块和入口，孩子们开始频繁问我："Miss利，这个按钮是干什么的？"

看着孩子们被复杂界面绊住手脚，我不禁想：为什么不能有一个专门为孩子设计的、清爽简单的AI绘画平台呢？

说干就干！我和一位技术特别好的合作伙伴开始了"秘密计划"。记得有一天晚上，我们视频连线调试平台，我负责设计体验流程，他负责实现代码。我们为了一个按钮该放哪儿还"小吵"了一架："放左边更直观！""不，放右边符合用户习惯！"就这样，常常忙到凌晨两三点，第二天还得顶着熊猫眼去上课。

功夫不负有心人，"Miss利@魔法画笔乐园"终于上线了！当孩子们第一次登录这个为他们量身定制的平台，亲手创作时，神奇的事情发生了——那些原本抽象的AI概念突然间变得鲜活无比！教室里到处是欢呼声：

"Miss利快看！我让AI画了一只会飞的猫！它飞得好高啊！"一个孩子兴奋地叫着

"我的恐龙有彩色的翅膀，还会喷七彩火焰！"另一个孩子也不甘示弱。

看着孩子们围在我身边，争先恐后地展示自己的作品，那一刻我忘记了所有熬夜的疲惫。教室里充满了惊叹和笑声，孩子们的眼睛里闪烁着创造的光芒——这大概就是教育最美的样子吧。

纲要中的启示和我实践中的发现不谋而合：
- 小学阶段重在认识AI基本概念、基本特征
- 通过生活中的AI应用案例，让孩子们感知AI的存在
- 体验式学习是小学阶段的核心方法

## 二、四维教学设计：让AI教育落地生根

### 1. 生活化情境：从孩子的世界出发

纲要强调要"从生活出发"，这点我深有体会。记得有次我精心准备了一堂关于"AI的应用场景"的课，列举了无人驾驶、智能医疗等"高大上"的例子，结果孩子们听得一脸茫然。

观察孩子们的反应，我就知道这堂课没有达到预期效果。与其说是"一脸茫然"，不如说是"礼貌性地听讲"。

回办公室路上，我不禁自问：为什么那些连我都觉得激动的AI应用，却引不起孩子们的共鸣？答案其实很简单——这些例子虽然前沿，却与8~10岁孩子的生活世界相距太远。

后来我换了思路，从孩子们熟悉的场景入手：

> "大家用过语音助手吗？"
>
> "有啊！我经常让小爱同学讲故事！"一位同学举手回答。
>
> "你们的照片是怎么自动分类的？"
>
> "我妈妈手机里的相册会自动把我的照片放在一起！"另外一位同学兴奋地说。

教室立刻活跃起来，孩子们纷纷分享自己的经历，AI概念不知不觉就被带入课堂了。

**实践案例：我的AI启蒙课堂**

在我的AI启蒙课上，我尝试让孩子们通过体验来理解AI。比如，我们一起使用"Miss利@魔法画笔乐园"平台创作图画，让孩子们感受AI如何将文字转化为图像；在课堂提问环节，我展示了简单的语音识别工具，孩子们兴奋地发现AI能"听懂"他们的问题。

有个平时不太爱发言的小朋友，在使用AI工具后主动举手分享："老师，AI好神奇，它能听懂我说什么，还能帮我画画！"看到他眼中的好奇和兴奋，我知道AI技术已经在他心中种下了种子。

这些贴近生活的小体验，让孩子们感受到AI不是遥不可及的高科技，而是能够理解和使用的有趣工具。

### 2. 游戏化体验：抽象概念的具象表达

纲要中提到小学阶段要"体验数据整理的方法"、"感知数据质量的基础作用"等，这些对小学生来说确实很抽象。

我的做法是通过游戏化设计，把这些概念变得可感知、可触摸：

**实践案例：图像分类小游戏**

在AI社团课上，我们玩"分类大师"游戏。我准备了一堆动物图片，让孩子们分组，每组负责按自己的标准给图片贴标签并分类。有的按照动物种类，有的按照颜色，还有的按照生活环境...

游戏结束后，我们一起讨论：

"为什么同样的图片，不同组的分类结果不一样？"

"因为我们关注的点不一样啊！"一个孩子回答道。

"如果让AI来分类，它需要知道什么？"

"它需要知道我们想按什么方式分类啊！"一个女孩回答。

"对！那我们怎么告诉它呢？"

"通过给图片贴上标签！"几个孩子异口同声地回答。

通过这样的游戏，孩子们不知不觉就理解了"数据标注"、"数据集"这些概念，还体会到了数据质量对AI学习的重要性。课后，一个小女孩拉着我的手说："Miss利，原来AI也是通过学习来认识世界的，只是它学习的方式和我们不一样！"

### 3. 创造性实践：从使用者到创造者

纲要中强调小学阶段要"尝试使用生成式人工智能工具开展简单的创作"，这正是我一直在努力的方向。

**实践案例：AI绘画创作**

在我们的"5步让孩子爱上AI绘画"实践中，孩子们从最初的好奇到后来能够熟练使用"创意三步曲"（画什么+长什么样+怎么画）来指导AI创作。

刚开始时，孩子们只会简单地输入"画一只猫"，得到的结果往往不尽如人意。经过引导，他们学会了更详细地描述自己的想法："画一只橘色的、毛茸茸的猫咪，它正在草地上玩耍，画面风格像宫崎骏的动画电影"。看到自己的语言能够创造出精美图像，孩子们既惊叹于AI的能力，又体会到了语言表达的力量。

我记得有次课上，一个小女孩用AI画了只"六条腿的猫"。我本以为她会失望，没想到她反而眼睛一亮："Miss利，你看！AI也会出错啊，它也像我们一样需要学习呢！"孩子们的思考有时候真的出乎意料，也刷新了我对小学阶段AI教育的认知。

这种从使用工具到理解工具局限性的转变，正是我们希望看到的认知提升。

### 4. 伦理启蒙：数字公民的责任培养

看到纲要也重视伦理责任，我心里暗暗点头 - 这正是我一直想传达给孩子们的。

**实践案例：智能宠物投喂器项目**

在"智能宠物投喂器"项目中，我们不仅关注技术实现，还讨论了很多伦理问题：

"如果投喂器误判断，宠物会不会挨饿？"一个戴眼镜的小男孩担忧地问。

"我们该不该完全依赖AI来照顾宠物？"另一个女孩提出质疑。

"技术应该服务于什么样的目标？"我引导大家思考。

这些讨论没有标准答案，但能引发孩子们深入思考。有个小男孩说的一句话，我到现在还记得清清楚楚："Miss利，我觉得我们不是在做机器诶，是在给狗狗找朋友。"

那一刻我才真正懂得，孩子们在技术学习中，已经开始思考技术与生命、与情感的关系，这正是伦理教育的最好切入点。

## 三、分层教学：不同年级段的差异化策略

纲要虽然把小学作为一个整体阶段，但我在实践中发现，低中高年级的孩子认知差异很大，需要差异化设计：

### 低年级：感知为主，玩中学

### 低年级：感知为主，玩中学

对1-2年级的孩子，我主要通过故事、游戏引入AI概念，让他们在玩中感知AI的存在。比如通过智能玩具的演示，让孩子们知道"这个机器人能听懂我说话"。

我还设计了简单的"AI故事时间"，用拟人化的方式讲解AI如何"看到"和"听到"东西。孩子们最喜欢的是"猜猜我是谁"游戏，我会给语音助手不同的提示，让它识别不同的动物或物品，孩子们听到准确的回答时总会惊呼连连。

低年级孩子虽然理解能力有限，但他们的想象力和模仿能力却很强。有一次课后，我看见几个一年级的孩子在角落里玩"我是AI助手"的游戏，一个孩子扮演用户提问，另一个装作AI回答，那认真的样子让我忍俊不禁。

随着年龄的增长，孩子们对AI的好奇心和操作能力也在提升，这就需要我们提供更丰富的动手体验。


### 中年级：初步认知，动手体验

3-4年级的孩子好奇心强，动手能力提升，我会设计更多让他们亲自操作的活动。比如在AI绘画课上，让他们尝试不同的提示词，观察生成结果的变化。

"AI创新探索营"就很受中年级孩子欢迎，他们通过简单的项目体验，初步理解AI是如何"学习"的。记得有个四年级的男孩在成功让AI按照他的描述画出一个恐龙战士后，满脸自豪地向全班展示："看！我让AI明白了我想要什么！"

### 高年级：引导思考，初步创造

5-6年级的孩子已经有了一定的逻辑思维能力，我会引导他们思考AI背后的原理，尝试简单的创新应用。

比如在"AI搜索进阶"课程中，我们从"要答案"到"会提问"，培养他们的问题分解能力和批判性思维。一位六年级的孩子告诉我："以前遇到不懂的问题，我就直接问AI'恐龙为什么灭绝'，现在我知道要先问'有哪些恐龙灭绝的证据'，再问'科学家有哪些解释理论'，最后再比较不同理论的可信度。"

着孩子们思维一点点成长，那种成就感比什么都珍贵。

## 四、实施挑战与解决策略

把纲要落实到实处，难免会遇到各种挑战，以下是我的一些应对策略：

### 资源有限怎么办？

### 资源有限怎么办？

并非每所学校都像我们一样拥有充足的电脑资源。在与其他学校老师交流时，我常被问到如何在设备有限的情况下开展AI教育。

同时，即使设备充足，我们依然面临着其他资源约束，比如学生的能力差异、可用的教学时间等。以下是我的一些应对策略：

1. **差异化学习**：虽然我们学校的电脑设备很充足，每个学生都能一人一机，但我很快发现了新的挑战——学生们的数字技能水平差异巨大。有些孩子在家已经接触过编程，而有些则连基本操作都不熟悉。

为了解决这个问题，我设计了"能力阶梯"学习任务，同一节课有三个不同难度的任务卡。

学生可以根据自己的能力选择合适的起点，完成后再挑战更高级别。这样既照顾了基础薄弱的学生，也能让能力强的孩子保持学习兴趣。


2. **替代方案**："不插电"活动也能教授AI概念，比如角色扮演"训练"同学完成特定任务。我们玩过一个游戏，让一个孩子扮演"AI"，其他孩子给它"编程"，通过指令让它在教室里行走到特定位置。

3. **线上资源**：善用国家智慧教育平台等免费资源。我经常从这些平台上找适合小学生的AI教育资源，有效弥补了学校资源的不足。

### 技术更新太快怎么办？

AI技术日新月异，教师很难跟上所有进展。我的策略是：

1. **抓住不变的核心**：数据、算法、算力这些核心概念是相对稳定的。我在教学中反复强调这些基础概念，不管技术怎么变，这些核心原理是不变的。

2. **与时俱进但不盲从**：定期了解新技术，但不必追求最新最酷。我每周会花一个晚上浏览AI最新动态，但只将真正有教育价值的引入课堂。

3. **与孩子共同学习**：坦然承认"老师也在学习"，师生一起探索。有次孩子问我一个关于大模型的问题，我坦诚地说："这个我也不太清楚，我们一起研究一下吧。"结果这反而激发了孩子们的探究热情，大家一起找资料、讨论，最后比我单独讲解效果还好。

### 如何评价学习效果？

纲要提出了学业质量评价标准，但如何落实到日常教学中呢？

1. **过程性评价**：关注孩子参与活动的表现，而非仅看最终作品。我会记录孩子们在活动中的表现、提问和思考过程，这些往往比最终作品更能反映学习效果。

2. **多元评价方式**：结合作品展示、小组讨论、个人反思等多种形式。我设计了"AI学习护照"，包含技能图谱、作品集、反思日记和同伴互评等多元评价内容。

3. **成长档案袋**：记录孩子在AI学习中的成长轨迹。每个孩子都有一个电子成长档案，保存他们在AI学习过程中的作品和思考，让他们和家长都能看到进步。

## 五、结语：种下AI教育的种子

回顾这两年的AI教育实践，再对照《课程指导纲要》，我感到既欣慰又期待。

欣慰的是，我们摸索出来的方法与官方政策方向高度一致；期待的是，有了这份纲要的指引，未来的AI教育之路会更加清晰。

作为小学教师，我们可能无法预见这些孩子未来会在AI领域取得什么成就，但我们今天播下的种子，一定会在未来生根发芽。

就像那个设计"智能宠物投喂器"的小男孩说的："我们不是在做机器，是在给狗狗找朋友。"这种将技术与温度、与生活、与情感相连接的思考，正是我们AI教育最宝贵的成果。

---

**与你分享：**

你在开展AI教育时，有哪些让你印象深刻的教学案例？遇到了哪些挑战？欢迎在评论区交流讨论！