<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>技术服务于真实生活</title>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            display: flex;
            flex-direction: column;
            align-items: center;
            background-color: #f5f7fa;
            padding: 20px;
            color: #333;
        }
        .container {
            max-width: 900px;
            margin-bottom: 30px;
        }
        .concept-card {
            background-color: white;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            padding: 30px;
            margin-bottom: 20px;
        }
        .main-title {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 20px;
            font-size: 28px;
            font-weight: bold;
        }
        .subtitle {
            color: #7f8c8d;
            text-align: center;
            margin-bottom: 30px;
            font-size: 16px;
            font-style: italic;
        }
        .quote {
            background-color: #f8f9fa;
            border-left: 4px solid #3498db;
            padding: 15px;
            margin: 20px 0;
            font-style: italic;
            color: #555;
        }
        .bridge-container {
            position: relative;
            height: 180px;
            margin: 30px 0;
            overflow: hidden;
        }
        .bridge {
            position: absolute;
            width: 100%;
            height: 10px;
            background: linear-gradient(90deg, #3498db, #9b59b6);
            top: 90px;
            border-radius: 5px;
            z-index: 1;
        }
        .tech-side {
            position: absolute;
            left: 0;
            top: 30px;
            width: 120px;
            height: 120px;
            background-color: #3498db;
            border-radius: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 48px;
            z-index: 2;
        }
        .life-side {
            position: absolute;
            right: 0;
            top: 30px;
            width: 120px;
            height: 120px;
            background-color: #e74c3c;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 48px;
            z-index: 2;
        }
        .connection-steps {
            position: absolute;
            display: flex;
            justify-content: space-between;
            width: 60%;
            left: 20%;
            top: 50px;
            z-index: 3;
        }
        .step {
            width: 80px;
            height: 80px;
            background: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 3px 10px rgba(0,0,0,0.1);
            font-size: 30px;
            color: #2c3e50;
            border: 3px solid #fff;
            position: relative;
        }
        .step-tooltip {
            position: absolute;
            bottom: -75px;
            left: 50%;
            transform: translateX(-50%);
            width: 180px;
            background-color: #2c3e50;
            color: white;
            padding: 8px 12px;
            border-radius: 6px;
            font-size: 14px;
            opacity: 0;
            transition: opacity 0.3s;
            text-align: center;
            pointer-events: none;
        }
        .step:hover .step-tooltip {
            opacity: 1;
        }
        .principles {
            margin: 40px 0;
        }
        .principle {
            margin-bottom: 25px;
            display: flex;
            align-items: flex-start;
        }
        .principle-icon {
            flex-shrink: 0;
            width: 60px;
            height: 60px;
            border-radius: 50%;
            margin-right: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            color: white;
        }
        .principle-1 .principle-icon { background-color: #3498db; }
        .principle-2 .principle-icon { background-color: #2ecc71; }
        .principle-3 .principle-icon { background-color: #e74c3c; }
        
        .principle-content {
            flex-grow: 1;
        }
        .principle-title {
            font-weight: bold;
            margin-bottom: 10px;
            color: #2c3e50;
            font-size: 18px;
        }
        .principle-desc {
            color: #7f8c8d;
            line-height: 1.6;
        }
        .examples {
            display: flex;
            justify-content: space-between;
            margin-top: 15px;
        }
        .example {
            flex: 1;
            background-color: #f8f9fa;
            padding: 12px;
            margin: 0 5px;
            border-radius: 6px;
            font-size: 14px;
            color: #555;
        }
        .download-btn {
            background-color: #3498db;
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            transition: all 0.3s;
            display: flex;
            align-items: center;
            margin: 30px auto 0;
            box-shadow: 0 3px 10px rgba(52,152,219,0.3);
        }
        .download-btn:hover {
            background-color: #2980b9;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(52,152,219,0.4);
        }
        .download-btn svg {
            margin-right: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="concept-card" id="infographic">
            <div class="main-title"></div>
            <div class="subtitle"></div>
            
            
            <div class="bridge-container">
                <div class="bridge"></div>
                <div class="tech-side">💻</div>
                <div class="life-side">🐕</div>
                <div class="connection-steps">
                    <div class="step" style="background-color: #f1c40f;">
                        🔗
                        <div class="step-tooltip">连接技术与生活的桥梁</div>
                    </div>
                    <div class="step" style="background-color: #2ecc71;">
                        💡
                        <div class="step-tooltip">激发创新思维</div>
                    </div>
                    <div class="step" style="background-color: #9b59b6;">
                        ❤️
                        <div class="step-tooltip">培养科技温度</div>
                    </div>
                </div>
            </div>
            
            <div class="principles">
                <div class="principle principle-1">
                    <div class="principle-icon">🔄</div>
                    <div class="principle-content">
                        <div class="principle-title">技术知识点与生活场景的有机融合</div>
                        <div class="principle-desc">
                            将抽象的人工智能概念与孩子们熟悉的日常场景相结合，让技术学习变得具体而有意义。通过真实问题的解决过程，孩子们能够自然地理解并应用技术知识。
                        </div>
                        <div class="examples">
                            <div class="example">
                                <strong>课堂实例:</strong> 通过"宠物声音"分析理解语音识别技术
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="principle principle-2">
                    <div class="principle-icon">🎯</div>
                    <div class="principle-content">
                        <div class="principle-title">项目任务必须回应真实需求</div>
                        <div class="principle-desc">
                            设计的项目应当解决孩子们能够理解和关心的实际问题，使技术学习过程充满目的性和意义感。真实需求驱动的项目能激发更深层次的学习动机。
                        </div>
                        <div class="examples">
                            <div class="example">
                                <strong>课堂实例:</strong> "宠物独自在家"这一真实问题激发设计智能投喂解决方案
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="principle principle-3">
                    <div class="principle-icon">🎨</div>
                    <div class="principle-content">
                        <div class="principle-title">保留孩子们创意的自由表达空间</div>
                        <div class="principle-desc">
                            在结构化教学框架内，为孩子们提供足够的创意自由度，鼓励他们提出个性化的解决方案和功能创新，培养技术创造力。
                        </div>
                        <div class="examples">
                            <div class="example">
                                <strong>课堂实例:</strong> 孩子们自发设计"宠物心情识别"拓展功能
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <button class="download-btn" id="downloadBtn">
        <svg width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
            <polyline points="7 10 12 15 17 10"></polyline>
            <line x1="12" y1="15" x2="12" y2="3"></line>
        </svg>
        下载"技术服务于真实生活"图表
    </button>

    <script src="https://html2canvas.hertzen.com/dist/html2canvas.min.js"></script>
    <script>
        document.getElementById('downloadBtn').addEventListener('click', function() {
            const element = document.getElementById('infographic');
            const originalWidth = element.offsetWidth;
            const originalHeight = element.offsetHeight;
            const scale = 2;
            
            html2canvas(element, {
                backgroundColor: null,
                scale: scale,
                width: originalWidth,
                height: originalHeight,
                windowWidth: originalWidth * scale,
                windowHeight: originalHeight * scale
            }).then(canvas => {
                const link = document.createElement('a');
                link.download = '技术服务于真实生活.png';
                link.href = canvas.toDataURL('image/png');
                link.click();
            });
        });
    </script>
</body>
</html>