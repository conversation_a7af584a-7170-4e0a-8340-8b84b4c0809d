<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>初先生的一人企业日记 - 公众号封面</title>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@300;400;500;700;900&display=swap" rel="stylesheet">
    <script src="https://html2canvas.hertzen.com/dist/html2canvas.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Noto Sans SC', sans-serif;
            background-color: #f0f9f4; /* 浅绿色背景，更柔和 */
            padding: 20px;
        }
        
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 6px 25px rgba(0,0,0,0.1); /* 增强阴影 */
            padding: 20px;
        }
        
        .covers-wrapper {
            display: flex;
            width: 100%;
            background-color: white;
            border-radius: 4px;
            overflow: hidden;
        }
        
        .main-cover {
            width: 70%;
            background-color: #079452;
            position: relative;
            overflow: hidden; /* 确保子元素不溢出 */
        }
        
        .circle-square {
            width: 30%;
            background-color: #079452;
            position: relative;
            overflow: hidden; /* 确保子元素不溢出 */
        }
        
        .main-content {
            padding: 30px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            height: 100%;
            text-align: center; /* 确保文本居中 */
        }
        
        .square-content {
            padding: 20px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            height: 100%;
            text-align: center; /* 确保文本居中 */
        }
        
        .main-title {
            font-size: 64px; /* 动态调整 */
            font-weight: 900;
            color: #FFD966;
            line-height: 1.1;
            margin-bottom: 15px;
        }
        
        .main-subtitle-major {
            font-size: 24px; /* 动态调整 */
            font-weight: 500;
            color: #FFD966;
            margin-bottom: 8px;
        }
        
        .main-subtitle-minor {
            font-size: 18px; /* 动态调整 */
            font-weight: 300; /* 更细的字重 */
            color: rgba(255, 255, 255, 0.8); /* 半透明白色 */
            margin-bottom: 25px;
        }
        
        .square-owner {
            font-size: 18px; /* 动态调整 */
            font-weight: 500;
            color: rgba(255, 255, 255, 0.9);
            margin-bottom: 5px;
        }
        
        .square-title {
            font-size: 28px; /* 动态调整 */
            font-weight: 700; /* 降低一点字重，避免太挤 */
            color: #FFD966;
            margin-bottom: 10px;
            line-height: 1.2;
        }
        
        .square-subtitle {
            font-size: 14px; /* 动态调整 */
            font-weight: 300;
            color: rgba(255, 217, 102, 0.8); /* 淡金黄色 */
        }
        
        .tag {
            background-color: rgba(255, 217, 102, 0.9); /* 更柔和的标签背景 */
            color: #079452;
            font-weight: 700;
            font-size: 16px; /* 动态调整 */
            padding: 8px 18px;
            border-radius: 20px;
        }
        
        .bg-pattern {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            opacity: 0.08; /* 降低透明度 */
            background-image: 
                linear-gradient(rgba(255,255,255,0.1) 1px, transparent 1px),
                linear-gradient(90deg, rgba(255,255,255,0.1) 1px, transparent 1px);
            background-size: 20px 20px; /* 更细密的网格 */
            pointer-events: none;
        }
        
        .download-btn {
            display: block;
            margin: 25px auto 0;
            padding: 12px 25px;
            background-color: #079452;
            color: white;
            border: none;
            border-radius: 6px; /* 更圆润的按钮 */
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            transition: background-color 0.3s, transform 0.2s;
        }
        
        .download-btn:hover {
            background-color: #057842;
            transform: translateY(-2px); /* 轻微上浮效果 */
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="covers-wrapper" id="coversWrapper">
            <!-- 左侧大封面 -->
            <div class="main-cover">
                <div class="bg-pattern"></div>
                <div class="main-content">
                    <h1 class="main-title">一人企业日记</h1>
                    <p class="main-subtitle-major">复利商业化动作</p>
                    <p class="main-subtitle-minor">每日实践记录</p>
                    <div class="tag">积少成多·日进一步</div>
                </div>
            </div>
            
            <!-- 右侧朋友圈封面 -->
            <div class="circle-square">
                <div class="bg-pattern"></div>
                <div class="square-content">
                    <p class="square-owner">初先生的</p>
                    <h2 class="square-title">一人企业日记</h2>
                    <p class="square-subtitle">复利商业化动作<br>每日实践记录</p>
                </div>
            </div>
        </div>
    </div>
    
    <button class="download-btn" id="downloadBtn">下载封面图片 (5x)</button>
    
    <script>
        function setDynamicSizes() {
            const mainCover = document.querySelector('.main-cover');
            const squareCover = document.querySelector('.circle-square');
            
            const mainWidth = mainCover.offsetWidth;
            mainCover.style.height = (mainWidth / 2.35) + 'px';
            
            const squareWidth = squareCover.offsetWidth;
            squareCover.style.height = squareWidth + 'px';
            
            // 动态调整字体大小
            document.querySelector('.main-title').style.fontSize = Math.min(mainWidth * 0.12, 64) + 'px';
            document.querySelector('.main-subtitle-major').style.fontSize = Math.min(mainWidth * 0.045, 24) + 'px';
            document.querySelector('.main-subtitle-minor').style.fontSize = Math.min(mainWidth * 0.035, 18) + 'px';
            document.querySelector('.tag').style.fontSize = Math.min(mainWidth * 0.03, 16) + 'px';
            
            document.querySelector('.square-owner').style.fontSize = Math.min(squareWidth * 0.09, 18) + 'px';
            document.querySelector('.square-title').style.fontSize = Math.min(squareWidth * 0.14, 28) + 'px';
            document.querySelector('.square-subtitle').style.fontSize = Math.min(squareWidth * 0.07, 14) + 'px';
        }
        
        window.addEventListener('load', setDynamicSizes);
        window.addEventListener('resize', setDynamicSizes);
        
        document.getElementById('downloadBtn').addEventListener('click', function() {
            const button = this;
            button.textContent = '正在生成图片...';
            button.disabled = true;

            html2canvas(document.getElementById('coversWrapper'), {
                backgroundColor: null, 
                scale: 5 // 提升到5倍分辨率
            }).then(function(canvas) {
                const link = document.createElement('a');
                link.download = '初先生的一人企业日记-封面-超高清5x.png';
                link.href = canvas.toDataURL('image/png');
                link.click();
                button.textContent = '下载封面图片 (5x)';
                button.disabled = false;
            }).catch(function(error) {
                console.error('oops, something went wrong!', error);
                button.textContent = '生成失败，请重试';
                button.disabled = false;
            });
        });
    </script>
</body>
</html>