<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>被四年级学生的AI作品震撼到了！- Miss利公众号封面</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://html2canvas.hertzen.com/dist/html2canvas.min.js"></script>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@300;400;500;700;900&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Noto Sans SC', sans-serif;
        }
        .cover-container {
            aspect-ratio: 3.35 / 1;
            width: 1005px;
            height: 300px;
        }
        .main-cover {
            aspect-ratio: 2.35 / 1;
            width: 705px;
            height: 300px;
        }
        .share-cover {
            aspect-ratio: 1 / 1;
            width: 300px;
            height: 300px;
        }
        .tech-grid {
            background-image: 
                linear-gradient(rgba(30, 144, 255, 0.1) 1px, transparent 1px),
                linear-gradient(90deg, rgba(30, 144, 255, 0.1) 1px, transparent 1px);
            background-size: 20px 20px;
        }
        .floating-icon {
            animation: float 3s ease-in-out infinite;
        }
        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }
        .glow-effect {
            box-shadow: 0 0 20px rgba(30, 144, 255, 0.3);
        }
    </style>
</head>
<body class="bg-gray-100 p-8">
    <div class="flex flex-col items-center space-y-6">
        <!-- 封面容器 -->
        <div id="cover-content" class="cover-container bg-gradient-to-r from-blue-500 to-blue-600 relative overflow-hidden flex">
            <!-- 科技网格背景 -->
            <div class="absolute inset-0 tech-grid opacity-30"></div>
            
            <!-- 装饰性图标 -->
            <div class="absolute top-4 left-4 floating-icon">
                <svg class="w-8 h-8 text-white opacity-20" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M12 2L2 7v10c0 5.55 3.84 10 9 11 1.09-.21 2-.4 3-.82V15h2v1.18c1-.42 1.91-.61 3-.82C19.16 17 23 12.55 23 7V7L12 2z"/>
                </svg>
            </div>
            
            <div class="absolute top-8 right-8 floating-icon" style="animation-delay: -1s;">
                <svg class="w-6 h-6 text-white opacity-20" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M9.4 16.6L4.8 12l4.6-4.6L8 6l-6 6 6 6 1.4-1.4zm5.2 0L19.2 12l-4.6-4.6L16 6l6 6-6 6-1.4-1.4z"/>
                </svg>
            </div>
            
            <div class="absolute bottom-4 left-8 floating-icon" style="animation-delay: -2s;">
                <svg class="w-7 h-7 text-white opacity-20" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                </svg>
            </div>
            
            <!-- 主封面区域 -->
            <div class="main-cover relative flex flex-col justify-center items-center text-center px-8">
                <!-- 主标题 -->
                <div class="text-white space-y-2">
                    <h1 class="text-4xl font-bold leading-tight">
                        被<span class="text-yellow-300 font-black">四年级学生</span>的
                    </h1>
                    <h1 class="text-4xl font-bold leading-tight">
                        <span class="text-yellow-300 font-black">AI作品</span>震撼到了！
                    </h1>
                </div>

                <!-- 副标题 -->
                <div class="mt-4 text-blue-100 text-lg font-medium">
                    《将进酒》AI动画 · 跨学科创新实践
                </div>

                <!-- 分类标签 -->
                <div class="mt-6 bg-white bg-opacity-20 px-4 py-2 rounded-full">
                    <span class="text-white text-sm font-medium">教学创新</span>
                </div>
            </div>
            
            <!-- 分享封面区域 -->
            <div class="share-cover relative flex flex-col justify-center items-center text-center px-6">
                <!-- 简化标题 -->
                <div class="text-white space-y-3">
                    <h2 class="text-2xl font-bold leading-tight">
                        四年级学生的
                    </h2>
                    <h2 class="text-2xl font-bold leading-tight">
                        <span class="text-yellow-300">AI作品</span>
                    </h2>
                    <h2 class="text-2xl font-bold leading-tight">
                        震撼了！
                    </h2>
                </div>
                
                <!-- 装饰图标 -->
                <div class="mt-4 glow-effect bg-white bg-opacity-20 p-3 rounded-full">
                    <svg class="w-8 h-8 text-yellow-300" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                    </svg>
                </div>
                
                <!-- Miss利标识 -->
                <div class="absolute bottom-4 right-4 text-white text-sm font-medium opacity-80">
                    Miss利
                </div>
            </div>
            
            <!-- 全局Miss利标识 -->
            <div class="absolute bottom-4 left-4 text-white text-sm font-medium opacity-60">
                Miss利 AI创新探索营
            </div>
        </div>
        
        <!-- 下载按钮 -->
        <button id="download-btn" class="bg-blue-500 hover:bg-blue-600 text-white font-bold py-3 px-8 rounded-lg transition-colors duration-200 shadow-lg">
            下载封面图片
        </button>
        
        <!-- 使用说明 -->
        <div class="text-gray-600 text-sm text-center max-w-md">
            <p>点击下载按钮可保存封面图片</p>
            <p class="mt-1">左侧为主封面，右侧为分享封面</p>
        </div>
    </div>

    <script>
        document.getElementById('download-btn').addEventListener('click', function() {
            const element = document.getElementById('cover-content');
            const button = this;
            
            // 禁用按钮并显示加载状态
            button.disabled = true;
            button.textContent = '生成中...';
            
            html2canvas(element, {
                scale: 2, // 提高清晰度
                useCORS: true,
                allowTaint: true,
                backgroundColor: null
            }).then(function(canvas) {
                // 创建下载链接
                const link = document.createElement('a');
                link.download = '被四年级学生的AI作品震撼到了-Miss利封面.png';
                link.href = canvas.toDataURL();
                link.click();
                
                // 恢复按钮状态
                button.disabled = false;
                button.textContent = '下载封面图片';
            }).catch(function(error) {
                console.error('生成图片失败:', error);
                alert('生成图片失败，请重试');
                
                // 恢复按钮状态
                button.disabled = false;
                button.textContent = '下载封面图片';
            });
        });
    </script>
</body>
</html>
