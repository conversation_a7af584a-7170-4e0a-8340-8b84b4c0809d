<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Miss利公众号封面 - 800多个孩子把平台用"崩"了</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://html2canvas.hertzen.com/dist/html2canvas.min.js"></script>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@400;500;700;900&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            font-family: 'Noto Sans SC', sans-serif;
        }
        .cover-container {
            aspect-ratio: 3.35 / 1;
            width: 100%;
            max-width: 1000px;
            height: auto;
        }
        .crash-shake {
            animation: shake 0.8s ease-in-out infinite;
        }
        @keyframes shake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-2px); }
            75% { transform: translateX(2px); }
        }
        .floating-element {
            animation: float 4s ease-in-out infinite;
        }
        .floating-element:nth-child(2) {
            animation-delay: 1.5s;
        }
        .floating-element:nth-child(3) {
            animation-delay: 3s;
        }
        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-8px) rotate(2deg); }
        }
        .tech-grid {
            background-image:
                linear-gradient(rgba(255, 255, 255, 0.1) 1px, transparent 1px),
                linear-gradient(90deg, rgba(255, 255, 255, 0.1) 1px, transparent 1px);
            background-size: 25px 25px;
        }
        .data-flow {
            position: absolute;
            width: 100%;
            height: 2px;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
            animation: flow 3s linear infinite;
        }
        @keyframes flow {
            0% { transform: translateX(-100%); }
            100% { transform: translateX(100%); }
        }
    </style>
</head>
<body class="bg-gray-100 p-4">
    <div class="max-w-6xl mx-auto">
        <h1 class="text-3xl font-bold text-center mb-8 text-gray-800">Miss利公众号封面设计</h1>
        
        <!-- 封面容器 -->
        <div id="cover-design" class="cover-container bg-gradient-to-br from-blue-500 via-blue-600 to-blue-800 relative overflow-hidden mx-auto flex tech-grid">

            <!-- 主封面区域 (2.35:1 = 70.1%) -->
            <div class="flex-none flex flex-col justify-center items-center relative tech-grid" style="width: 70.1%; height: 100%; background: linear-gradient(135deg, #3b82f6 0%, #1e40af 50%, #1e3a8a 100%);">

                <!-- 背景装饰元素 -->
                <div class="absolute inset-0 overflow-hidden">
                    <!-- 服务器图标 -->
                    <div class="floating-element absolute top-6 right-8 text-white/20">
                        <i class="fas fa-server text-4xl"></i>
                    </div>

                    <!-- 用户群体图标 -->
                    <div class="floating-element absolute bottom-8 left-6 text-white/15">
                        <i class="fas fa-users text-3xl"></i>
                    </div>

                    <!-- 警告图标 -->
                    <div class="crash-shake absolute top-1/2 right-12 text-red-400/30 transform -translate-y-1/2">
                        <i class="fas fa-exclamation-triangle text-5xl"></i>
                    </div>

                    <!-- 数据流动效果 -->
                    <div class="data-flow" style="top: 30%;"></div>
                    <div class="data-flow" style="top: 70%; animation-delay: 1.5s;"></div>

                    <!-- 科技线条 -->
                    <div class="absolute top-1/4 left-1/4 w-24 h-0.5 bg-white/20 transform rotate-12"></div>
                    <div class="absolute bottom-1/3 right-1/4 w-20 h-0.5 bg-white/15 transform -rotate-12"></div>
                </div>

                <!-- 主标题内容 -->
                <div class="text-center z-10 relative px-4">
                    <div class="mb-3">
                        <span class="text-5xl font-black" style="color: #FF6B6B; text-shadow: 0 0 20px rgba(255, 107, 107, 0.8);">800多个</span>
                        <span class="text-white text-4xl font-bold ml-2">孩子</span>
                    </div>
                    <div class="text-white text-3xl font-bold leading-tight">
                        把平台用<span class="crash-shake text-4xl font-black" style="color: #FF4444; text-shadow: 0 0 15px rgba(255, 68, 68, 0.8);">"崩"</span>了
                    </div>
                    <div class="text-blue-100 text-lg font-medium mt-4">
                        第四期AI创新探索营的大惊喜来了！
                    </div>
                </div>
            </div>
            
            <!-- 分享封面区域 (1:1 = 29.9%) -->
            <div class="flex-none flex flex-col justify-center items-center relative tech-grid" style="width: 29.9%; height: 100%; background: linear-gradient(135deg, #1e40af 0%, #1e3a8a 50%, #312e81 100%);">

                <!-- 分享封面装饰 -->
                <div class="absolute inset-0 overflow-hidden">
                    <!-- 小图标装饰 -->
                    <div class="floating-element absolute top-4 right-4 text-white/25">
                        <i class="fas fa-bolt text-xl"></i>
                    </div>
                    <div class="floating-element absolute bottom-4 left-4 text-white/20">
                        <i class="fas fa-child text-lg"></i>
                    </div>

                    <!-- 小数据流 -->
                    <div class="absolute top-1/3 left-0 w-full h-0.5 bg-white/10"></div>
                    <div class="absolute bottom-1/3 left-0 w-full h-0.5 bg-white/10"></div>

                    <!-- 科技点缀 -->
                    <div class="absolute top-6 left-6 w-2 h-2 bg-white/20 rounded-full"></div>
                    <div class="absolute bottom-6 right-6 w-1.5 h-1.5 bg-white/15 rounded-full"></div>
                </div>

                <!-- 分享版标题 - 使用最简单的方式确保显示 -->
                <div class="text-center px-2 w-full z-20 relative">
                    <div class="mb-2">
                        <div class="text-3xl font-black" style="color: #FF6B6B !important; text-shadow: 0 0 15px rgba(255, 107, 107, 0.8) !important;">800个</div>
                    </div>
                    <div class="text-white text-lg font-bold leading-tight mb-1" style="text-shadow: 2px 2px 4px rgba(0,0,0,0.8) !important;">
                        孩子把平台
                    </div>
                    <div class="text-white text-lg font-bold leading-tight mb-3" style="text-shadow: 2px 2px 4px rgba(0,0,0,0.8) !important;">
                        用<span class="crash-shake text-xl font-black" style="color: #FF4444 !important; text-shadow: 0 0 10px rgba(255, 68, 68, 0.8) !important;">"崩"</span>了
                    </div>
                    <div class="text-white text-sm font-medium" style="text-shadow: 1px 1px 2px rgba(0,0,0,0.8) !important;">
                        AI创新探索营惊喜
                    </div>
                </div>
            </div>
            
            <!-- Miss利品牌标识 -->
            <div class="absolute bottom-3 right-4 text-white text-sm font-bold" style="z-index: 999;">
                Miss利
            </div>
        </div>
        
        <!-- 下载按钮 -->
        <div class="text-center mt-8">
            <button id="download-btn" class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-4 px-8 rounded-lg transition-all duration-300 shadow-lg hover:shadow-xl">
                下载封面图片
            </button>
        </div>
        
        <!-- 测试说明 -->
        <div class="mt-8 p-6 bg-white rounded-lg shadow-lg">
            <h2 class="text-xl font-bold mb-4 text-gray-800">简化版测试说明</h2>
            <div class="text-gray-600 space-y-2">
                <p><strong>布局方式：</strong>使用flexbox布局，确保左右区域严格按比例分配</p>
                <p><strong>左侧主封面：</strong>70.1%宽度，2.35:1比例</p>
                <p><strong>右侧分享封面：</strong>29.9%宽度，1:1比例</p>
                <p><strong>文字处理：</strong>使用!important确保样式不被覆盖，简化层级结构</p>
                <p><strong>如果右侧还是没有文字，请检查浏览器控制台是否有错误信息</strong></p>
            </div>
        </div>
    </div>

    <script>
        document.getElementById('download-btn').addEventListener('click', function() {
            const element = document.getElementById('cover-design');
            const button = this;
            
            button.innerHTML = '生成中...';
            button.disabled = true;
            
            html2canvas(element, {
                scale: 2,
                useCORS: true,
                allowTaint: true,
                backgroundColor: null
            }).then(function(canvas) {
                const link = document.createElement('a');
                link.download = 'Miss利-800个孩子把平台用崩了-封面-v3.png';
                link.href = canvas.toDataURL('image/png', 1.0);
                link.click();
                
                button.innerHTML = '下载封面图片';
                button.disabled = false;
            }).catch(function(error) {
                console.error('下载失败:', error);
                button.innerHTML = '下载封面图片';
                button.disabled = false;
                alert('下载失败，请重试');
            });
        });
        
        // 调试信息
        console.log('页面加载完成');
        console.log('分享封面区域:', document.querySelector('.cover-container > div:last-child'));
    </script>
</body>
</html>
