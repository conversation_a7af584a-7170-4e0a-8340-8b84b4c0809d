<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Miss利个人资料卡 - 16:9版本</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@5.15.4/css/all.min.css" rel="stylesheet">
    <style>
        body { 
            background-color: #f0f8ff;
            margin: 0;
            padding: 0;
            height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            overflow: hidden;
        }
        .card-container {
            width: 100%;
            height: 100%;
            display: flex;
            justify-content: center;
            align-items: center;
        }
        .card { 
            background: linear-gradient(180deg, #7ac2ff 0%, #f4efef 85%, rgba(244, 239, 239, 0.8) 100%);
            box-shadow: 0 15px 30px rgba(122, 194, 255, 0.2);
            border-radius: 24px;
            width: 100%;
            height: 100%;
            max-width: 1600px;
            max-height: 900px; /* 16:9 比例 */
            display: flex;
            overflow: hidden;
        }
        .left-panel {
            width: 35%;
            padding: 40px;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            background: linear-gradient(135deg, #7ac2ff 0%, #5a9fe5 100%);
            color: white;
        }
        .right-panel {
            width: 65%;
            padding: 40px;
            background-color: rgba(255, 255, 255, 0.95);
            /* overflow-y: auto; */ /* 移除，允许内容撑开 */
        }
        .profile-section {
            display: flex;
            flex-direction: column;
            align-items: center;
            text-align: center;
            margin-bottom: 40px;
        }
        .profile-image {
            width: 180px;
            height: 180px;
            border-radius: 50%;
            border: 4px solid white;
            box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
            object-fit: cover;
            margin-bottom: 20px;
        }
        .section { 
            background-color: rgba(255, 255, 255, 0.1);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 24px;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        .right-section {
            background-color: rgba(255, 255, 255, 0.7);
            border-radius: 16px;
            padding: 16px; /* 恢复内边距 */
            margin-bottom: 16px; /* 恢复外边距 */
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(122, 194, 255, 0.08);
            border: 1px solid rgba(122, 194, 255, 0.1);
        }
        .right-section:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 18px rgba(122, 194, 255, 0.12);
        }
        .expertise-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 16px; /* 恢复网格间隙 */
        }
        .expertise-item {
            background: linear-gradient(145deg, #ffffff, #f8f9fa);
            border-radius: 12px;
            padding: 16px; /* 保持或恢复项目内边距 */
            transition: all 0.3s ease;
            border: 1px solid rgba(122, 194, 255, 0.15);
            box-shadow: 0 2px 8px rgba(122, 194, 255, 0.06);
        }
        .expertise-item:hover {
            transform: translateY(-2px);
            background-color: rgba(255, 255, 255, 0.9);
            box-shadow: 0 4px 12px rgba(122, 194, 255, 0.1);
        }
        .interest-tags {
            display: flex;
            flex-wrap: wrap;
            justify-content: center;
            gap: 10px;
        }
        .interest-tag-edu {
            background-color: #FADADD; /* 淡茱萸粉 */
            color: #555555; /* 深灰色文字 */
            padding: 8px 16px;
            border-radius: 20px;
            display: inline-block;
            transition: all 0.3s ease;
            box-shadow: 0 2px 6px rgba(250, 218, 221, 0.5); /* 匹配背景色的阴影 */
            border: 1px solid rgba(250, 218, 221, 0.8); /* 匹配背景色的边框 */
            font-size: 1rem;
        }
        .interest-tag-tech {
            background-color: #E0E7FF; /* 柔和淡蓝紫 */
            color: #555555; /* 深灰色文字 */
            padding: 8px 16px;
            border-radius: 20px;
            display: inline-block;
            transition: all 0.3s ease;
            box-shadow: 0 2px 6px rgba(224, 231, 255, 0.5); /* 匹配背景色的阴影 */
            border: 1px solid rgba(224, 231, 255, 0.8); /* 匹配背景色的边框 */
            font-size: 1rem;
        }
        .interest-tag-family {
            background-color: #FFF5E1; /* 奶油黄 */
            color: #555555; /* 深灰色文字 */
            padding: 8px 16px;
            border-radius: 20px;
            display: inline-block;
            transition: all 0.3s ease;
            box-shadow: 0 2px 6px rgba(255, 245, 225, 0.5); /* 匹配背景色的阴影 */
            border: 1px solid rgba(255, 245, 225, 0.8); /* 匹配背景色的边框 */
            font-size: 1rem;
        }
        .interest-tag-edu:hover, .interest-tag-tech:hover, .interest-tag-family:hover {
            transform: translateY(-1px);
            filter: brightness(1.1);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
        }
        .contact-item {
            display: flex;
            align-items: center;
            margin-bottom: 12px;
            color: white;
        }
        .contact-item i {
            margin-right: 12px;
            font-size: 1.2rem;
        }
        .qr-section {
            display: flex;
            flex-direction: column;
            align-items: center;
            margin-top: 30px;
        }
        .qr-code {
            width: 140px;
            height: 140px;
            background: white;
            border-radius: 12px;
            padding: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
            margin-bottom: 12px;
        }
        .text-primary { color: #4a90e2; font-weight: 600; }
        .text-white-title { color: white; font-weight: 600; }
        .section-title {
            font-size: 1.4rem;
            margin-bottom: 12px; /* 恢复标题下方间距 */
            display: flex;
            align-items: center;
        }
        .section-title i {
            margin-right: 12px;
        }
        .slogan {
            margin-top: 20px;
            font-size: 1.2rem;
            font-weight: 500;
            color: white;
            text-align: center;
        }
        /* 滚动条美化 - 由于移除了 overflow-y: auto，这些不再需要 */
        /*
        .right-panel::-webkit-scrollbar {
            width: 8px;
        }
        .right-panel::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.4);
            border-radius: 10px;
        }
        .right-panel::-webkit-scrollbar-thumb {
            background: rgba(122, 194, 255, 0.5);
            border-radius: 10px;
        }
        .right-panel::-webkit-scrollbar-thumb:hover {
            background: rgba(122, 194, 255, 0.7);
        }
        */
    </style>
</head>
<body>
    <div class="card-container">
        <div class="card">
            <!-- 左侧面板：个人信息、联系方式和二维码 -->
            <div class="left-panel">
                <div class="profile-section">
                    <img src="https:/img.ctnas.com/ctnas-img/2024/11/ce66b915460b812f4342cf78314260bc.202411241640299.png" 
                         alt="Profile" 
                         class="profile-image">
                    <h2 class="text-2xl font-bold mb-2">Miss 利</h2>
                    <p class="text-lg mb-3">信息科技教师 | AI教与学探索与分享者 | Maggie妈妈</p>
                    <p class="flex items-center">
                        <i class="fas fa-map-marker-alt mr-2"></i>顺德
                    </p>
                </div>
                
                <div class="section">
                    <h3 class="text-white-title section-title">
                        <i class="fas fa-heart"></i>兴趣爱好
                    </h3>
                    <div class="interest-tags">
                        <span class="interest-tag-edu">📚 教育创新</span>
                        <span class="interest-tag-tech">💻 科技探索</span>
                        <span class="interest-tag-family">💖 亲子成长</span> 
                    </div>
                </div>
                
                <div class="qr-section">
                    <img src="https:/img.ctnas.com/ctnas-img/2024/12/1ac4bc34b4a065e6d7cce27e9001a9ed.202412022259113.png" 
                         alt="QR Code" 
                         class="qr-code">
                    <div class="text-center">
                        <p class="text-white font-medium mb-2">扫码关注"Miss利AI研习笔记"</p>
                        <p class="slogan">分享AI实践 · 赋能智慧教育</p>
                    </div>
                </div>
            </div>
            
            <!-- 右侧面板：职业信息和专长 -->
            <div class="right-panel">
                <!-- 近期关注 -->
                <div class="right-section">
                    <h3 class="text-primary section-title">
                        <i class="fas fa-bullseye"></i>近期关注
                    </h3>
                    <ul class="text-gray-700 space-y-2 text-lg"> <!-- text-base 改回 text-lg -->
                        <li>信息科技教育创新：深耕AI教学融合、专业竞赛指导与前沿实践探索。</li>
                        <li>辅导科创竞赛：涵盖创客、模型、编程、人工智能等，激发学生科技潜能与实践能力。</li>
                        <li>主导"AI创新探索营"；打造"Miss利@魔法画笔乐园"绘画教学平台，赋能青少年AI创想力。</li>
                    </ul>
                </div>

                <!-- 职业亮点 -->
                <div class="right-section">
                    <h3 class="text-primary section-title">
                        <i class="fas fa-award"></i>职业亮点
                    </h3>
                    <ul class="text-gray-700 pl-5 list-disc space-y-2 text-lg"> <!-- text-base 改回 text-lg -->
                        <li>深耕信息科技教育创新，打造AI+教学特色课程体系</li>
                        <li>运用创新教学法辅导孩子们在全国、省、市级信息科技大赛中屡获佳绩</li>
                        <li>多次荣获"优秀辅导教师"、"金牌教练"等称号</li>
                    </ul>
                </div>

                <!-- 专长领域 -->
                <div class="right-section">
                    <h3 class="text-primary section-title">
                        <i class="fas fa-bolt"></i>专长领域
                    </h3>
                    <div class="expertise-grid">
                        <div class="expertise-item">
                            <h4 class="font-semibold text-primary mb-2 text-lg">信息科技教育</h4>
                            <p class="text-gray-600 text-lg">十四载教学经验，培养孩子们科技创新思维</p>
                        </div>
                        <div class="expertise-item">
                            <h4 class="font-semibold text-primary mb-2 text-lg">科技创新竞赛</h4>
                            <p class="text-gray-600 text-lg">提供个性化竞赛指导，激发孩子们创造潜能</p>
                        </div>
                        <div class="expertise-item">
                            <h4 class="font-semibold text-primary mb-2 text-lg">人工智能教育探索</h4>
                            <p class="text-gray-600 text-lg">将人工智能技术融入课堂教学，设计趣味化智慧学习场景</p>
                        </div>
                        <div class="expertise-item">
                            <h4 class="font-semibold text-primary mb-2 text-lg">AI创意教育</h4>
                            <p class="text-gray-600 text-lg">开展"AI创新探索营"，引导孩子们进行人工智能创作</p>
                        </div>
                    </div>
                </div>
                
                <!-- 教育理念 -->
                <div class="right-section"> <!-- 移除 mb-1，使用通用的 margin-bottom -->
                    <h3 class="text-primary section-title">
                        <i class="fas fa-lightbulb"></i>教育理念
                    </h3>
                    <p class="text-gray-700 text-lg leading-relaxed"> <!-- text-base 改为 text-lg -->
                        用有温度的科技教育，点亮每个孩子的独特闪光点。
                    </p>
                </div>
                
                <!-- 未来展望 (已按要求删除) -->
            </div>
        </div>
    </div>
</body>
</html> 