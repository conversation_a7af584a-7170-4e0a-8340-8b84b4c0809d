<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>「Miss利」公众号封面生成器 - AI点燃奇思妙想</title>
    
    <!-- Tailwind CSS, Google Fonts, Font Awesome -->
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@400;700;900&family=Poppins:wght@600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    
    <!-- html2canvas -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script>

    <style>
        body {
            font-family: 'Noto Sans SC', sans-serif;
            background-color: #f0f2f5;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            min-height: 100vh;
            padding: 2rem;
        }

        .font-poppins {
            font-family: 'Poppins', sans-serif;
        }

        #cover-container {
            display: flex;
            width: 100%;
            max-width: 1200px;
            margin: 20px auto;
            aspect-ratio: 3.35 / 1;
            background-color: #1E90FF;
            overflow: hidden;
            box-shadow: 0 10px 25px rgba(0,0,0,0.2);
        }

        .cover-part {
            position: relative;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            color: white;
            padding: 5%;
            overflow: hidden;
            height: 100%;
        }

        .main-cover { flex: 2.35; }
        .share-cover { flex: 1; }
        
        .bg-tech-innovation {
            background-color: #1E90FF;
            background-image:
                radial-gradient(circle at 15% 20%, rgba(135, 206, 250, 0.3), transparent 40%),
                radial-gradient(circle at 85% 80%, rgba(70, 130, 180, 0.4), transparent 40%),
                linear-gradient(rgba(255,255,255,0.05) 1px, transparent 1px),
                linear-gradient(90deg, rgba(255,255,255,0.05) 1px, transparent 1px),
                linear-gradient(135deg, #4682B4 0%, #1E90FF 100%);
            background-size: 100% 100%, 100% 100%, 30px 30px, 30px 30px, 100% 100%;
        }

        .text-container {
            text-align: center;
            line-height: 1.4;
            font-weight: 700;
            width: 90%; 
        }
        
        .main-cover .text-container { transform: scale(1); }
        
        .share-cover .text-container {
            transform: scale(0.80); 
        }

        .title-main {
            font-size: clamp(1rem, 7.5vw, 4.5rem); 
            font-weight: 900;
            letter-spacing: 0.05em;
        }
        .share-cover .title-main { line-height: 1.25; }

        .title-secondary {
            font-size: clamp(0.6rem, 3.5vw, 2rem);
            font-weight: 400;
            opacity: 0.9;
            margin-top: 0.5em;
        }
        .share-cover .title-secondary { line-height: 1.3; }

        .title-sub {
            font-size: clamp(0.5rem, 3vw, 1.5rem);
            font-weight: 400;
            margin-bottom: 0.5em;
            opacity: 0.9;
            text-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
        }
        
        .text-gradient {
            background-image: linear-gradient(180deg, #FFFFFF 20%, #7FFFD4 100%);
            -webkit-background-clip: text;
            background-clip: text;
            color: transparent;
            filter: drop-shadow(0 2px 8px rgba(0, 0, 0, 0.3));
        }

        .highlight-text {
            color: #00FFFF; 
            text-shadow: 0 0 10px #00FFFF, 0 0 20px rgba(0, 255, 255, 0.7);
        }

        .brand-logo {
            position: absolute;
            bottom: 5%;
            right: 5%;
            font-size: clamp(0.4rem, 1.8vw, 1rem); 
            font-weight: 400;
            color: white;
            opacity: 0.8;
            background: rgba(0,0,0,0.2);
            padding: 0.2em 0.6em;
            border-radius: 4px;
            display: flex;
            align-items: center;
        }

        .deco-icon {
            position: absolute;
            color: rgba(255, 255, 255, 0.15);
            font-size: clamp(2rem, 10vw, 7rem);
            z-index: 0;
            filter: blur(1px);
        }
        .icon-top-left { top: -5%; left: -5%; transform: rotate(-15deg); }
        .icon-bottom-right { bottom: -10%; right: -5%; transform: rotate(15deg); }

        #download-btn {
            margin-top: 2rem;
            padding: 1rem 2.5rem;
            font-size: 1.1rem;
            font-weight: 600;
            color: white;
            background-image: linear-gradient(135deg, #1E90FF 0%, #4169E1 100%);
            border: none;
            border-radius: 50px;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(30, 144, 255, 0.4);
        }
        #download-btn:hover { transform: translateY(-3px); box-shadow: 0 8px 20px rgba(30, 144, 255, 0.5); }
        #download-btn:active { transform: translateY(-1px); }
    </style>
</head>
<body>

    <div id="cover-container">
        <!-- Main Cover -->
        <div class="main-cover cover-part bg-tech-innovation">
            <!-- UPDATED ICONS -->
            <i class="fas fa-lightbulb deco-icon icon-top-left"></i>
            <i class="fas fa-comments deco-icon icon-bottom-right"></i>
            
            <div class="text-container relative z-10">
                <!-- UPDATED TEXT -->
                <div class="title-sub">AI创新探索营 · 第六天</div>
                <div class="title-main text-gradient">
                    <span class="font-poppins highlight-text">AI</span>点燃奇思妙想
                </div>
                <div class="title-secondary text-gradient">他们的想法远超你的想象</div>
            </div>
            <div class="brand-logo">Miss利<span class="font-poppins mx-2">|</span>科技教育</div>
        </div>

        <!-- Share Cover (Optimized) -->
        <div class="share-cover cover-part bg-tech-innovation">
             <div class="text-container relative z-10">
                 <!-- UPDATED TEXT with manual line breaks -->
                <div class="title-sub">AI创新探索营 · 第六天</div>
                <div class="title-main text-gradient">
                    <span class="font-poppins highlight-text">AI</span>点燃<br>奇思妙想
                </div>
                <div class="title-secondary text-gradient">
                    他们的想法<br>远超你的想象
                </div>
            </div>
            <div class="brand-logo">Miss利</div>
        </div>
    </div>

    <button id="download-btn">
        <i class="fas fa-download mr-2"></i> 下载封面图片
    </button>

    <script>
        document.getElementById('download-btn').addEventListener('click', function() {
            const container = document.getElementById('cover-container');
            const button = this;
            button.textContent = '正在生成图片...';
            button.disabled = true;

            html2canvas(container, {
                useCORS: true, 
                allowTaint: true,
                scale: 3, 
                backgroundColor: null,
            }).then(canvas => {
                const link = document.createElement('a');
                // UPDATED FILENAME
                link.download = 'Miss利-AI创新探索营第六天-奇思妙想.png';
                link.href = canvas.toDataURL('image/png');
                link.click();
                
                button.innerHTML = '<i class="fas fa-download mr-2"></i> 下载封面图片';
                button.disabled = false;
            }).catch(error => {
                console.error('图片生成失败:', error);
                alert('图片生成失败，请检查浏览器控制台获取错误信息。');
                button.innerHTML = '<i class="fas fa-download mr-2"></i> 下载封面图片';
                button.disabled = false;
            });
        });
    </script>

</body>
</html>