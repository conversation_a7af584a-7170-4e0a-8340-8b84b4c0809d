<div style="background-color: #ffffff; padding: 25px; border-radius: 16px; margin: 20px 0; box-shadow: 0 5px 15px rgba(0,0,0,0.08); font-family: 'Microsoft YaHei', sans-serif; max-width: 800px; margin-left: auto; margin-right: auto;">
  
  <!-- 标题 -->
  <h3 style="color: #2b6cb0; text-align: center; margin-bottom: 25px; font-weight: 600; position: relative;">
    <span style="background: white; padding: 0 20px; position: relative;">分层教学四维策略</span>
    <div style="height: 2px; background: linear-gradient(90deg, transparent, #2b6cb0, transparent); position: absolute; top: 50%; left: 0; right: 0; z-index: -1;"></div>
  </h3>
  
  <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 20px;">
    
    <!-- 策略1 -->
    <div style="background-color: #f0f9ff; border-radius: 12px; overflow: hidden; border-top: 4px solid #63b3ed;">
      <div style="height: 120px; background: #ebf8ff; display: flex; justify-content: center; align-items: center;">
        <img src="https://img.icons8.com/color/96/000000/group-foreground-selected.png" alt="小专家指导" style="height: 80px;">
        <!-- 实际使用请替换为合法图片链接 -->
      </div>
      <div style="padding: 15px;">
        <h4 style="color: #2c5282; margin-top: 0; margin-bottom: 10px;">小专家轮岗制</h4>
        <p style="margin: 0; color: #4a5568; font-size: 14px; line-height: 1.5;">让掌握技能的孩子指导同伴，促进同伴互助学习</p>
      </div>
    </div>
    
    <!-- 策略2 -->
    <div style="background-color: #fff5f0; border-radius: 12px; overflow: hidden; border-top: 4px solid #f68773;">
      <div style="height: 120px; background: #fff8f5; display: flex; justify-content: center; align-items: center;">
        <img src="https://img.icons8.com/color/96/000000/faq.png" alt="问题银行" style="height: 80px;">
      </div>
      <div style="padding: 15px;">
        <h4 style="color: #9b2c2c; margin-top: 0; margin-bottom: 10px;">问题银行系统</h4>
        <p style="margin: 0; color: #4a5568; font-size: 14px; line-height: 1.5;">建立动态问题数据库，分类标记高频疑问，实现精准答疑，减少重复提问</p>
      </div>
    </div>
    
    <!-- 策略3 -->
    <div style="background-color: #f0fff4; border-radius: 12px; overflow: hidden; border-top: 4px solid #68d391;">
      <div style="height: 120px; background: #f0fff7; display: flex; justify-content: center; align-items: center;">
        <img src="https://img.icons8.com/color/96/000000/break.png" alt="问题分解" style="height: 80px;">
      </div>
      <div style="padding: 15px;">
        <h4 style="color: #276749; margin-top: 0; margin-bottom: 10px;">把复杂问题分解</h4>
        <p style="margin: 0; color: #4a5568; font-size: 14px; line-height: 1.5;">将难题拆解为可操作的小任务，降低认知负荷</p>
      </div>
    </div>
    
    <!-- 策略4 -->
    <div style="background-color: #faf5ff; border-radius: 12px; overflow: hidden; border-top: 4px solid #b794f4;">
      <div style="height: 120px; background: #f9f5ff; display: flex; justify-content: center; align-items: center;">
        <img src="https://img.icons8.com/color/96/000000/stairs.png" alt="分层任务" style="height: 80px;">
      </div>
      <div style="padding: 15px;">
        <h4 style="color: #553c9a; margin-top: 0; margin-bottom: 10px;">双轨任务设计</h4>
        <p style="margin: 0; color: #4a5568; font-size: 14px; line-height: 1.5;">基础版确保达标，进阶版提供挑战，支持个性化学习路径选择</p>
      </div>
    </div>
    
  </div>
  
  <div style="text-align: center; margin-top: 25px; font-size: 13px; color: #718096; font-style: italic;">
    教学实践数据表明：采用分层策略后，学生参与度提升40%，高阶思维表现提高35%
  </div>
</div>