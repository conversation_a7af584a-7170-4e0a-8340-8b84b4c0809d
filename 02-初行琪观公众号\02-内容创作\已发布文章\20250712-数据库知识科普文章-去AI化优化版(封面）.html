<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据库避坑指南：一文搞懂数据库选择 - 初行琪观</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;800&family=Noto+Sans+SC:wght@400;500;600;700;800&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://html2canvas.hertzen.com/dist/html2canvas.min.js"></script>
    <style>
        .cover-container {
            aspect-ratio: 3.35 / 1;
            font-family: 'Noto <PERSON>s SC', 'Inter', sans-serif;
        }
        .main-cover {
            aspect-ratio: 2.35 / 1;
        }
        .circle-cover {
            aspect-ratio: 1 / 1;
        }
        .tech-grid {
            background-image: 
                linear-gradient(rgba(7, 148, 82, 0.1) 1px, transparent 1px),
                linear-gradient(90deg, rgba(7, 148, 82, 0.1) 1px, transparent 1px);
            background-size: 20px 20px;
        }
        .data-flow {
            background: linear-gradient(45deg, transparent 30%, rgba(255, 217, 102, 0.1) 50%, transparent 70%);
            animation: flow 3s ease-in-out infinite;
        }
        @keyframes flow {
            0%, 100% { transform: translateX(-100%); }
            50% { transform: translateX(100%); }
        }
        .circuit-line {
            position: relative;
        }
        .circuit-line::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 0;
            right: 0;
            height: 2px;
            background: linear-gradient(90deg, #079452, #FFD966, #079452);
            transform: translateY(-50%);
            opacity: 0.3;
        }
        .floating-icon {
            animation: float 2s ease-in-out infinite;
        }
        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }
        .glow-text {
            text-shadow: 0 0 20px rgba(7, 148, 82, 0.3);
        }
        .gradient-text {
            background: linear-gradient(135deg, #079452, #FFD966);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
    </style>
</head>
<body class="bg-gray-100 p-4">
    <div id="cover-design" class="cover-container w-full max-w-6xl mx-auto bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 relative overflow-hidden flex">
        <!-- 科技背景网格 -->
        <div class="absolute inset-0 tech-grid opacity-30"></div>
        
        <!-- 数据流动效果 -->
        <div class="absolute inset-0">
            <div class="data-flow absolute top-1/4 w-full h-1"></div>
            <div class="data-flow absolute top-2/4 w-full h-1" style="animation-delay: 1s;"></div>
            <div class="data-flow absolute top-3/4 w-full h-1" style="animation-delay: 2s;"></div>
        </div>

        <!-- 主封面区域 (2.35:1) -->
        <div class="main-cover flex-1 relative p-8 flex flex-col justify-center">
            <!-- 装饰性科技元素 -->
            <div class="absolute top-6 left-6 floating-icon">
                <i class="fas fa-database text-2xl text-emerald-500 opacity-60"></i>
            </div>
            <div class="absolute top-6 right-6 floating-icon" style="animation-delay: 0.5s;">
                <i class="fas fa-server text-2xl text-yellow-400 opacity-60"></i>
            </div>
            <div class="absolute bottom-6 left-6 floating-icon" style="animation-delay: 1s;">
                <i class="fas fa-cloud text-2xl text-emerald-400 opacity-60"></i>
            </div>
            <div class="absolute bottom-6 right-6 floating-icon" style="animation-delay: 1.5s;">
                <i class="fas fa-cogs text-2xl text-yellow-500 opacity-60"></i>
            </div>

            <!-- 主标题区域 -->
            <div class="relative z-10 text-center">
                <!-- 副标题 -->
                <div class="text-yellow-400 text-lg font-medium mb-4 tracking-wider">
                    <i class="fas fa-compass mr-2"></i>
                    TECH GUIDE
                </div>
                
                <!-- 主标题 -->
                <h1 class="text-white text-4xl font-bold leading-tight mb-6 glow-text">
                    <span class="block text-5xl gradient-text font-black">数据库避坑指南</span>
                    <span class="block text-2xl mt-2 text-gray-300">一文搞懂数据库选择</span>
                </h1>

                <!-- 价值标签 -->
                <div class="flex justify-center space-x-4 mb-6">
                    <span class="bg-emerald-500 text-white px-3 py-1 rounded-full text-sm font-medium">
                        <i class="fas fa-shield-alt mr-1"></i>避坑指南
                    </span>
                    <span class="bg-yellow-500 text-slate-900 px-3 py-1 rounded-full text-sm font-medium">
                        <i class="fas fa-lightbulb mr-1"></i>实战经验
                    </span>
                    <span class="bg-slate-600 text-white px-3 py-1 rounded-full text-sm font-medium">
                        <i class="fas fa-rocket mr-1"></i>技术选型
                    </span>
                </div>

                <!-- 品牌标识 -->
                <div class="text-emerald-400 text-sm font-medium tracking-wider">
                    初行琪观 · AI探索笔记
                </div>
            </div>

            <!-- 装饰性电路线 -->
            <div class="absolute bottom-0 left-0 right-0 circuit-line h-1"></div>
        </div>

        <!-- 朋友圈封面区域 (1:1) -->
        <div class="circle-cover w-auto relative bg-gradient-to-br from-emerald-600 to-emerald-800 flex flex-col justify-center items-center p-6">
            <!-- 背景装饰 -->
            <div class="absolute inset-0 bg-gradient-to-br from-transparent via-emerald-500/10 to-yellow-400/10"></div>
            
            <!-- 科技图标背景 -->
            <div class="absolute top-4 left-4 text-white/20 text-3xl">
                <i class="fas fa-database"></i>
            </div>
            <div class="absolute bottom-4 right-4 text-white/20 text-3xl">
                <i class="fas fa-server"></i>
            </div>
            <div class="absolute top-4 right-4 text-white/20 text-2xl">
                <i class="fas fa-cloud"></i>
            </div>
            <div class="absolute bottom-4 left-4 text-white/20 text-2xl">
                <i class="fas fa-cogs"></i>
            </div>

            <!-- 中心内容 -->
            <div class="relative z-10 text-center text-white">
                <div class="text-yellow-300 text-xs font-medium mb-2 tracking-wider">
                    TECH GUIDE
                </div>
                <h2 class="text-lg font-bold leading-tight mb-3">
                    <span class="block text-xl font-black">数据库</span>
                    <span class="block text-xl font-black">避坑指南</span>
                </h2>
                <div class="text-xs text-emerald-200 mb-3">
                    一文搞懂数据库选择
                </div>
                <div class="text-xs text-yellow-300 font-medium">
                    初行琪观
                </div>
            </div>

            <!-- 装饰性边框 -->
            <div class="absolute inset-2 border border-white/20 rounded-lg"></div>
        </div>
    </div>

    <!-- 下载按钮 -->
    <div class="text-center mt-6">
        <button onclick="downloadImage()" class="bg-emerald-600 hover:bg-emerald-700 text-white font-medium py-3 px-8 rounded-lg transition-colors duration-200 shadow-lg">
            <i class="fas fa-download mr-2"></i>
            下载封面图片
        </button>
    </div>

    <script>
        function downloadImage() {
            const element = document.getElementById('cover-design');
            const button = document.querySelector('button');
            
            button.textContent = '正在生成图片...';
            button.disabled = true;
            
            html2canvas(element, {
                scale: 2,
                backgroundColor: null,
                useCORS: true,
                allowTaint: true
            }).then(canvas => {
                const link = document.createElement('a');
                link.download = '数据库避坑指南-初行琪观封面.png';
                link.href = canvas.toDataURL();
                link.click();
                
                button.innerHTML = '<i class="fas fa-download mr-2"></i>下载封面图片';
                button.disabled = false;
            }).catch(error => {
                console.error('生成图片失败:', error);
                button.innerHTML = '<i class="fas fa-download mr-2"></i>下载封面图片';
                button.disabled = false;
                alert('图片生成失败，请重试');
            });
        }
    </script>
</body>
</html>