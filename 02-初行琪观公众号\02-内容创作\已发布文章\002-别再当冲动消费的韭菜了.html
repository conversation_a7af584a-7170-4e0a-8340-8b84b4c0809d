<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>初行琪观 - 知识付费清醒指南</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@400;500;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://html2canvas.hertzen.com/dist/html2canvas.min.js"></script>
    <style>
        :root {
            --primary-color: #079452;
            --accent-color: #FFD966;
            --dark-color: #0A2A1F;
            --light-color: #F9FAFB;
        }
        
        body {
            font-family: 'Noto Sans SC', sans-serif;
            background-color: #f0f0f0;
            padding: 20px;
        }
        
        .container-wrapper {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .cover-container {
            width: 100%;
            position: relative;
            background-color: white;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
            display: flex;
            overflow: hidden;
        }
        
        .cover-container::before {
            content: '';
            display: block;
            padding-top: 29.8%; /* 保持整体 3.35:1 的比例 */
        }
        
        .main-cover {
            position: absolute;
            top: 0;
            left: 0;
            width: 70.15%; /* 2.35/3.35 = 70.15% */
            height: 100%;
            background-color: var(--primary-color);
            overflow: hidden;
        }
        
        .circle-cover {
            position: absolute;
            top: 0;
            right: 0;
            width: 29.85%; /* 1/3.35 = 29.85% */
            height: 100%;
            background-color: var(--primary-color);
            overflow: hidden;
        }
        
        .circuit-lines {
            position: absolute;
            width: 100%;
            height: 100%;
            opacity: 0.1;
            background-image: 
                radial-gradient(circle at 10% 20%, white 1px, transparent 1px),
                radial-gradient(circle at 90% 80%, white 1px, transparent 1px),
                linear-gradient(to right, rgba(255,255,255,0.05) 1px, transparent 1px),
                linear-gradient(to bottom, rgba(255,255,255,0.05) 1px, transparent 1px);
            background-size: 60px 60px, 60px 60px, 20px 20px, 20px 20px;
            background-position: 0 0;
        }
        
        .data-flow {
            position: absolute;
            width: 100%;
            height: 100%;
            overflow: hidden;
        }
        
        .data-flow::before {
            content: '';
            position: absolute;
            top: -10%;
            left: -10%;
            width: 120%;
            height: 120%;
            background: radial-gradient(ellipse at center, rgba(255,217,102,0.2) 0%, transparent 70%);
        }
        
        .title-container {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 85%;
            text-align: center;
            z-index: 10;
        }
        
        .main-title {
            font-size: 72px;
            font-weight: 700;
            color: white;
            margin-bottom: 12px;
            line-height: 1.2;
        }
        
        .subtitle {
            font-size: 36px;
            font-weight: 500;
            color: var(--accent-color);
            margin-bottom: 18px;
        }
        
        .brand-name {
            font-size: 1.5vw;
            font-weight: 500;
            color: rgba(255, 255, 255, 0.8);
            margin-top: 2vw;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .brand-name i {
            margin-right: 0.5vw;
        }
        
        .highlight {
            color: var(--accent-color);
            font-weight: 700;
            display: inline-block;
            position: relative;
        }
        
        .highlight::after {
            content: '';
            position: absolute;
            bottom: -3px;
            left: 0;
            width: 100%;
            height: 4px;
            background-color: var(--accent-color);
            opacity: 0.5;
        }
        
        .value-tags {
            display: flex;
            justify-content: center;
            margin-top: 15px;
            gap: 12px;
            align-items: center;
        }
        
        .tag {
            background-color: rgba(255, 255, 255, 0.1);
            padding: 5px 12px;
            border-radius: 30px;
            font-size: 14px;
            color: var(--light-color);
            display: flex;
            align-items: center;
            justify-content: center;
            line-height: 1;
            height: 26px;
        }
        
        .tag i {
            margin-right: 6px;
            font-size: 12px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            position: relative;
            top: -1px;
        }
        
        .circle-content {
            position: absolute;
            width: 80%;
            height: 80%;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            text-align: center;
        }
        
        .circle-title {
            font-size: 32px;
            font-weight: 700;
            color: white;
            margin-bottom: 12px;
            line-height: 1.3;
        }
        
        .circle-subtitle {
            font-size: 26px;
            color: var(--accent-color);
            margin-bottom: 12px;
        }
        
        .download-btn {
            margin-top: 20px;
            padding: 10px 20px;
            background-color: var(--primary-color);
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
        }
        
        .download-btn:hover {
            background-color: #05773F;
            transform: translateY(-2px);
            box-shadow: 0 4px 10px rgba(7, 148, 82, 0.3);
        }
        
        .download-btn i {
            margin-right: 8px;
        }
        
        /* AI元素装饰 */
        .ai-elements {
            position: absolute;
            width: 100%;
            height: 100%;
            top: 0;
            left: 0;
            pointer-events: none;
        }
        
        .ai-node {
            position: absolute;
            width: 2vw;
            height: 2vw;
            border-radius: 50%;
            background-color: rgba(255, 217, 102, 0.1);
            box-shadow: 0 0 10px rgba(255, 217, 102, 0.5);
        }
        
        .ai-node:nth-child(1) {
            top: 15%;
            left: 10%;
            width: 1.5vw;
            height: 1.5vw;
        }
        
        .ai-node:nth-child(2) {
            top: 75%;
            left: 15%;
            width: 1vw;
            height: 1vw;
        }
        
        .ai-node:nth-child(3) {
            top: 25%;
            right: 35%;
            width: 2vw;
            height: 2vw;
        }
        
        .ai-node:nth-child(4) {
            top: 60%;
            right: 15%;
            width: 1.2vw;
            height: 1.2vw;
        }
        
        .ai-line {
            position: absolute;
            background-color: rgba(255, 217, 102, 0.2);
            height: 1px;
        }
        
        .ai-line:nth-child(5) {
            top: 20%;
            left: 5%;
            width: 15%;
            transform: rotate(30deg);
        }
        
        .ai-line:nth-child(6) {
            top: 70%;
            left: 10%;
            width: 25%;
            transform: rotate(-20deg);
        }
        
        .ai-line:nth-child(7) {
            top: 30%;
            right: 25%;
            width: 20%;
            transform: rotate(15deg);
        }
        
        /* 防韭菜元素 */
        .leek-protection {
            position: absolute;
            bottom: 10%;
            right: 45%;
            width: 45px;
            height: 45px;
            display: flex;
            justify-content: center;
            align-items: center;
            border-radius: 50%;
            background-color: rgba(255, 255, 255, 0.1);
            z-index: 5;
        }
        
        .leek-protection i {
            font-size: 22px;
            color: var(--accent-color);
        }
    </style>
</head>
<body>
    <div class="container-wrapper">
        <div class="cover-container" id="coverImage">
            <div class="main-cover">
                <div class="circuit-lines"></div>
                <div class="data-flow"></div>
                <div class="ai-elements">
                    <div class="ai-node"></div>
                    <div class="ai-node"></div>
                    <div class="ai-node"></div>
                    <div class="ai-node"></div>
                    <div class="ai-line"></div>
                    <div class="ai-line"></div>
                    <div class="ai-line"></div>
                </div>
                
                <div class="title-container">
                    <div class="main-title">
                        知识付费<span class="highlight">清醒指南</span>
                    </div>
                    <div class="subtitle">
                        别再当冲动消费的"韭菜"了
                    </div>
                    
                    <div class="value-tags">
                        <div class="tag"><i class="fas fa-shield-alt"></i><span>少亏钱</span></div>
                        <div class="tag"><i class="fas fa-money-bill-wave"></i><span>多赚钱</span></div>
                        <div class="tag"><i class="fas fa-hand-holding-usd"></i><span>少花冤枉钱</span></div>
                    </div>
                </div>
                
                <div class="leek-protection">
                    <i class="fas fa-shield-alt"></i>
                </div>
            </div>
            
            <div class="circle-cover">
                <div class="circuit-lines"></div>
                <div class="data-flow"></div>
                
                <div class="circle-content">
                    <div class="circle-title">
                        知识付费清醒指南
                    </div>
                    <div class="circle-subtitle">
                        告别盲目消费
                    </div>
                </div>
            </div>
        </div>
        
        <button id="downloadBtn" class="download-btn">
            <i class="fas fa-download"></i> 下载封面图片
        </button>
    </div>
    
    <script>
        document.getElementById('downloadBtn').addEventListener('click', function() {
            // 使用html2canvas捕获元素
            html2canvas(document.getElementById('coverImage')).then(function(canvas) {
                // 创建一个下载链接
                const link = document.createElement('a');
                link.download = '知识付费清醒指南.png';
                link.href = canvas.toDataURL('image/png');
                link.click();
            });
        });
    </script>
</body>
</html> 