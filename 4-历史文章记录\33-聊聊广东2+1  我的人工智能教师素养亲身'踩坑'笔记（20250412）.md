最近有朋友私信我：

> "你这公众号是不是被盗号了？怎么静悄悄这么久？"

哈哈，没有盗号，就是忙得不可开交！

这段时间我几乎**"人间蒸发"**，白天日常教学繁忙，每周都要准备公开课，晚上全扑在一件事上：继续升级我的「Miss利@魔法画笔乐园」，周末也在加班。

老实说，这个功能升级比我想象的要复杂得多。就在我熬了好几个晚上，终于解决图片存储问题的第二天，广东省教育厅举行了一场发布会！

公布了**"2+1"方案**：《广东省中小学教师人工智能素养框架》《广东省中小学学生人工智能素养框架》和《广东省中小学人工智能课程指导纲要》三份试行文件。

我边看边惊讶得下巴都要掉了——这不就是我摸索了两年的方向吗？！

我的AI实践方向居然与政策导向高度吻合哈！

[图片]

## 划重点：

接下来我就结合自己这两年在一线教学中的各种尝试（包括失败经历...），聊聊这股人工智能教育的官方**"东风"**到底给我们带来了什么，以及我是怎么提前**"撞"**上了政策方向的。

## 从迷茫到清晰的探索之路

刚看到这个五维度框架时，我差点笑出声——简直是把我这两年的教学探索日记完美归纳了！

意识理念、知识技能、实践应用、专业发展和社会责任，看着这五个维度，我脑海里全是这些年遇到的各种情境、学生和问题。

[图片]

### 意识理念：从"酷炫工具"到"教育哲学"

我一开始对人工智能的理解超级肤浅。记得2022年chatGPT刚出来的时候，纯粹觉得**"哇，这工具好酷！"**

然后就迫不及待地尝试，后来慢慢出现了人工智能绘画工具，想着**"孩子们看到一定会惊呆的！"**

当时条件有限，我只能录制展示视频搬到课堂给孩子们进行分享。

结果呢？确实惊呆了，但只是通过看视频，没办法体验，给到他们的感觉好像很遥远。

有个四年级小男孩直接问我：

> "Miss利，跟我们为什么要学信息科技有什么关系啊？"

这个问题当时把我问住了...那一晚我躺床上翻来覆去睡不着（老师被学生一问就容易钻牛角尖，你们懂的...），脑子里全是人工智能教育到底是啥玩意儿这种灵魂拷问。

现在我才恍然大悟，原来人工智能压根不是什么课堂上的**“高大上装饰品”**，它就是帮我们换种思考方式的一把**“钥匙”**啊！

课堂上我不再让孩子们机械地操作工具软件，而是引导他们思考：

> "如果人工智能可以替代部分人类工作，我们该如何与人工智能共存？"

[图片]

### 知识技能：从"我不行"到"原来我可以"

去年暑假，当我想开发**「Miss利@魔法画笔乐园」**，请教几位技术大佬朋友时，他们都不约而同直接泼冷水：

> "这个开发周期长而且费用也不低，如果只是用于个人教学，就没必要投资那么多费用，别闹了。"

但看着孩子们在课上没法沉浸式实践，逼着我必须试一试：市面上那些AI工具全是成人思维，孩子们用得直挠头，有得太多商业广告不利于教学，于是我开启了**"拼命三郎"**模式——白天上课，晚上和先生一起**“装模作样”**研发，经常是我出创意他敲代码，我俩还为一个按钮放哪儿能吵（笑）到凌晨两三点...第二天还得顶着黑眼圈去上课。

最艰难的是平台1.0上线后，当全部孩子一起使用，现场卡顿崩溃！

有些孩子可以正常出图，有些不能出图，我尴尬得想钻进地缝。

[图片]

但孩子们却异常冷静：

> "Miss利，这就是你说的失败也是学习的一部分吗？"

然后我们一起测试找bug，没想到这个**“意外”**变成了最生动的一课。

后面找出bug的原因是共用一条线路并发数量限制的问题，目前已有完善的解决方案。

这段经历教会我：教师的技术学习曲线可能很陡，但这恰恰是最好的教学素材。

如今我向孩子们展示平台新功能时，会顺便讲讲背后我翻过的**"山"**，他们总是听得特别入神。

[图片]

### 实践应用：智能宠物投喂器里藏着的人工智能教育秘密

说到**"实践应用"**，政策文件写得挺清楚的，不过真正让我理解这四个字含义的，是去年那个**"智能宠物投喂器"**项目。

还记得那次人工智能社团开课，孩子们本来准备了几个**"高大上"**的项目选题，结果一投票，**"宠物无人照料解决方案"**居然获胜了。

当时有个小男生举手解释：

> "我放学回家晚，狗狗总是饿着肚子等我，能不能做个自动投喂的东西？"

看着他认真的表情，我突然意识到，这才是真正打动孩子们的项目啊！

我采用**"问题分析→方案设计→实施验证→优化迭代"**的四步教学法，将复杂的语音识别技术转化为一系列孩子们能理解的具体任务。整个过程充满了意外收获和感动瞬间...

刚开始我也担心四年级的孩子能不能理解语音识别这么复杂的技术...结果他们学得比我想象中快多了！

后来这个小项目还被教育部课程教材研究所选中做了交流。李锋教授用**"情境式问题解决"**这个术语点评，很专业地肯定了我们的方法。

与此同时，也有一个瞬间让我难以忘怀：一个瘦瘦的小男孩下课偷偷跟我说的话：

> "Miss利，我觉得我们不是在做机器诶，是在给狗狗找朋友。"

听到这句，我那天回家路上一直在笑，这句话我看到了技术背后，孩子们心中那份纯粹的同理心和项目的温度。

[图片]

详细过程我写在了《当科技遇见童心：一次有温度的人工智能教育实践》那篇文章里，有兴趣可以去看看。

从这个项目中我算是真正懂了，人工智能教育不能光讲概念和原理。

当孩子们为解决真实问题而学习时，那种投入感和创造力是课本永远教不出来的。

现在我设计新课程都会问自己：这个技术能帮孩子们解决什么实际问题？如果答不上来，那可能就不是个好课题。

我猜政策里说的**"实践应用"**，说白了就是：别光纸上谈兵！让技术真正解决孩子们在乎的问题，哪怕只是个狗狗饿肚子这样看似简单的小事。

不过话说回来，孩子们关心的真问题有时候还真挺出乎我意料的！

### 专业发展与社会责任：“懂行人”的意外旅程

关于专业发展这点，我有时候觉得自己是被**"逼"**出来的...

学校里我成了**"人工智能百事通"**——老师们有什么人工智能问题都来问我：

> "Miss利，Deepseek如何本地部署？我的电脑配置可以吗？"

家长更夸张，从**"AI会不会让孩子变笨"**到**"孩子会不会让AI写作业？"**，各种问题接连轰炸，当我有时间也会耐心解答各种疑问。

这两年我读了很多书和资料，被迫成长为**“懂行人”**。

但每次看到原本对人工智能又怕又抗拒的家长和同事，慢慢开始尝试，甚至兴奋地跟我分享他们的第一次人工智能体验，那个瞬间真的...嗯...怎么说呢？有点像看着自己的学生突然**“开窍”**的那种暗爽，懂的都懂！

[图片]

看到这个五维度框架时，我第一反应是：

> "哇，原来我这两年的瞎摸索还挺“专业”的嘛！"

就像你以为自己在野路子上狂奔，结果一转身发现：咦？我居然不小心跑在了**“官方赛道”**上？

这种感觉，怎么说呢...有点像那种考试前没复习，结果发现题目全是你会的那种意外惊喜！（虽然我这**“复习”**也是熬了无数个加班夜...）

---

来聊聊呗：你们觉得这五个维度里哪个最难搞？

我猜肯定有人跟我一样，对那个**"知识技能"**维度头大不已吧？

技术真的追不上啊，我现在都养成习惯每周留一个晚上专门**"恶补"**新工具，不然真的要被学生问倒了...

你们有没有啥高效学习的小妙招？评论区见！